<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class CaseStudy extends Model
{
    use HasFactory, HasSlug;

    protected $fillable = [
        'title',
        'slug',
        'client_name',
        'client_location',
        'description',
        'content',
        'featured_image',
        'services_provided',
        'metrics',
        'pdf_file',
        'meta_title',
        'meta_description',
        'is_published',
        'published_at',
        'sort_order'
    ];

    protected $casts = [
        'services_provided' => 'array',
        'metrics' => 'array',
        'is_published' => 'boolean',
        'published_at' => 'datetime'
    ];

    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('title')
            ->saveSlugsTo('slug');
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }
}
