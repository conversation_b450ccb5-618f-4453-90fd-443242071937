<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Home page
Route::get('/', function () {
    return view('home');
})->name('home');

// About page
Route::get('/about-us', function () {
    return view('about');
})->name('about');

// Contact page
Route::get('/contact-us', function () {
    return view('contact');
})->name('contact');

Route::post('/contact-us', [App\Http\Controllers\ContactController::class, 'submit'])->name('contact.submit');

// Careers page
Route::get('/careers', function () {
    return view('careers');
})->name('careers');

// Service pages
Route::prefix('services')->name('services.')->group(function () {
    Route::get('/website-development', function () {
        return view('services.website-development');
    })->name('website-development');

    Route::get('/affiliate-marketing', function () {
        return view('services.affiliate-marketing');
    })->name('affiliate-marketing');

    Route::get('/pr-branding', function () {
        return view('services.pr-branding');
    })->name('pr-branding');

    Route::get('/email-marketing', function () {
        return view('services.email-marketing');
    })->name('email-marketing');

    Route::get('/content-marketing', function () {
        return view('services.content-marketing');
    })->name('content-marketing');

    Route::get('/social-media-marketing', function () {
        return view('services.social-media-marketing');
    })->name('social-media-marketing');

    Route::get('/seo-marketing', function () {
        return view('services.seo-marketing');
    })->name('seo-marketing');

    Route::get('/account-based-marketing', function () {
        return view('services.account-based-marketing');
    })->name('account-based-marketing');

    Route::get('/ppc-marketing', function () {
        return view('services.ppc-marketing');
    })->name('ppc-marketing');

    Route::get('/hubspot-marketing', function () {
        return view('services.hubspot-marketing');
    })->name('hubspot-marketing');

    Route::get('/saas-marketing', function () {
        return view('services.saas-marketing');
    })->name('saas-marketing');

    Route::get('/performance-marketing', function () {
        return view('services.performance-marketing');
    })->name('performance-marketing');

    Route::get('/subscription-marketing', function () {
        return view('services.subscription-marketing');
    })->name('subscription-marketing');

    Route::get('/answer-engine-optimization', function () {
        return view('services.answer-engine-optimization');
    })->name('answer-engine-optimization');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', function () {
        return view('blog.index');
    })->name('index');

    Route::get('/{slug}', function ($slug) {
        return view('blog.show', compact('slug'));
    })->name('show');
});

// Press releases routes
Route::prefix('press-release')->name('press-releases.')->group(function () {
    Route::get('/', function () {
        return view('press-releases.index');
    })->name('index');

    Route::get('/{slug}', function ($slug) {
        return view('press-releases.show', compact('slug'));
    })->name('show');
});

// Case studies routes
Route::prefix('case-studies')->name('case-studies.')->group(function () {
    Route::get('/', function () {
        return view('case-studies.index');
    })->name('index');

    Route::get('/{slug}', function ($slug) {
        return view('case-studies.show', compact('slug'));
    })->name('show');
});

// Whitepapers routes
Route::prefix('whitepapers')->name('whitepapers.')->group(function () {
    Route::get('/', function () {
        return view('whitepapers.index');
    })->name('index');

    Route::get('/{slug}', function ($slug) {
        return view('whitepapers.show', compact('slug'));
    })->name('show');
});
