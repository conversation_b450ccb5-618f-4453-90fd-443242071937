{"version": 3, "sources": ["../../@alpinejs/intersect/dist/module.esm.js"], "sourcesContent": ["// packages/intersect/src/index.js\nfunction src_default(Alpine) {\n  Alpine.directive(\"intersect\", Alpine.skipDuringClone((el, { value, expression, modifiers }, { evaluateLater, cleanup }) => {\n    let evaluate = evaluateLater(expression);\n    let options = {\n      rootMargin: getRootMargin(modifiers),\n      threshold: getThreshold(modifiers)\n    };\n    let observer = new IntersectionObserver((entries) => {\n      entries.forEach((entry) => {\n        if (entry.isIntersecting === (value === \"leave\"))\n          return;\n        evaluate();\n        modifiers.includes(\"once\") && observer.disconnect();\n      });\n    }, options);\n    observer.observe(el);\n    cleanup(() => {\n      observer.disconnect();\n    });\n  }));\n}\nfunction getThreshold(modifiers) {\n  if (modifiers.includes(\"full\"))\n    return 0.99;\n  if (modifiers.includes(\"half\"))\n    return 0.5;\n  if (!modifiers.includes(\"threshold\"))\n    return 0;\n  let threshold = modifiers[modifiers.indexOf(\"threshold\") + 1];\n  if (threshold === \"100\")\n    return 1;\n  if (threshold === \"0\")\n    return 0;\n  return Number(`.${threshold}`);\n}\nfunction getLengthValue(rawValue) {\n  let match = rawValue.match(/^(-?[0-9]+)(px|%)?$/);\n  return match ? match[1] + (match[2] || \"px\") : void 0;\n}\nfunction getRootMargin(modifiers) {\n  const key = \"margin\";\n  const fallback = \"0px 0px 0px 0px\";\n  const index = modifiers.indexOf(key);\n  if (index === -1)\n    return fallback;\n  let values = [];\n  for (let i = 1; i < 5; i++) {\n    values.push(getLengthValue(modifiers[index + i] || \"\"));\n  }\n  values = values.filter((v) => v !== void 0);\n  return values.length ? values.join(\" \").trim() : fallback;\n}\n\n// packages/intersect/builds/module.js\nvar module_default = src_default;\nexport {\n  module_default as default,\n  src_default as intersect\n};\n"], "mappings": ";;;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,UAAU,aAAa,OAAO,gBAAgB,CAAC,IAAI,EAAE,OAAO,YAAY,UAAU,GAAG,EAAE,eAAe,QAAQ,MAAM;AACzH,QAAI,WAAW,cAAc,UAAU;AACvC,QAAI,UAAU;AAAA,MACZ,YAAY,cAAc,SAAS;AAAA,MACnC,WAAW,aAAa,SAAS;AAAA,IACnC;AACA,QAAI,WAAW,IAAI,qBAAqB,CAAC,YAAY;AACnD,cAAQ,QAAQ,CAAC,UAAU;AACzB,YAAI,MAAM,oBAAoB,UAAU;AACtC;AACF,iBAAS;AACT,kBAAU,SAAS,MAAM,KAAK,SAAS,WAAW;AAAA,MACpD,CAAC;AAAA,IACH,GAAG,OAAO;AACV,aAAS,QAAQ,EAAE;AACnB,YAAQ,MAAM;AACZ,eAAS,WAAW;AAAA,IACtB,CAAC;AAAA,EACH,CAAC,CAAC;AACJ;AACA,SAAS,aAAa,WAAW;AAC/B,MAAI,UAAU,SAAS,MAAM;AAC3B,WAAO;AACT,MAAI,UAAU,SAAS,MAAM;AAC3B,WAAO;AACT,MAAI,CAAC,UAAU,SAAS,WAAW;AACjC,WAAO;AACT,MAAI,YAAY,UAAU,UAAU,QAAQ,WAAW,IAAI,CAAC;AAC5D,MAAI,cAAc;AAChB,WAAO;AACT,MAAI,cAAc;AAChB,WAAO;AACT,SAAO,OAAO,IAAI,SAAS,EAAE;AAC/B;AACA,SAAS,eAAe,UAAU;AAChC,MAAI,QAAQ,SAAS,MAAM,qBAAqB;AAChD,SAAO,QAAQ,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,QAAQ;AACjD;AACA,SAAS,cAAc,WAAW;AAChC,QAAM,MAAM;AACZ,QAAM,WAAW;AACjB,QAAM,QAAQ,UAAU,QAAQ,GAAG;AACnC,MAAI,UAAU;AACZ,WAAO;AACT,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,WAAO,KAAK,eAAe,UAAU,QAAQ,CAAC,KAAK,EAAE,CAAC;AAAA,EACxD;AACA,WAAS,OAAO,OAAO,CAAC,MAAM,MAAM,MAAM;AAC1C,SAAO,OAAO,SAAS,OAAO,KAAK,GAAG,EAAE,KAAK,IAAI;AACnD;AAGA,IAAI,iBAAiB;", "names": []}