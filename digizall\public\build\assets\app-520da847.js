function Vr(e,t){return function(){return e.apply(t,arguments)}}const{toString:Bs}=Object.prototype,{getPrototypeOf:Rn}=Object,{iterator:Lt,toStringTag:Gr}=Symbol,It=(e=>t=>{const n=Bs.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),ae=e=>(e=e.toLowerCase(),t=>It(t)===e),kt=e=>t=>typeof t===e,{isArray:Fe}=Array,et=kt("undefined");function zs(e){return e!==null&&!et(e)&&e.constructor!==null&&!et(e.constructor)&&Q(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ur=ae("ArrayBuffer");function Fs(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ur(e.buffer),t}const $s=kt("string"),Q=kt("function"),Wr=kt("number"),Rt=e=>e!==null&&typeof e=="object",js=e=>e===!0||e===!1,pt=e=>{if(It(e)!=="object")return!1;const t=Rn(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Gr in e)&&!(Lt in e)},Hs=ae("Date"),qs=ae("File"),Vs=ae("Blob"),Gs=ae("FileList"),Us=e=>Rt(e)&&Q(e.pipe),Ws=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Q(e.append)&&((t=It(e))==="formdata"||t==="object"&&Q(e.toString)&&e.toString()==="[object FormData]"))},Ks=ae("URLSearchParams"),[Xs,Ys,Js,Zs]=["ReadableStream","Request","Response","Headers"].map(ae),Qs=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function nt(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,i;if(typeof e!="object"&&(e=[e]),Fe(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;let a;for(r=0;r<o;r++)a=s[r],t.call(null,e[a],a,e)}}function Kr(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,i;for(;r-- >0;)if(i=n[r],t===i.toLowerCase())return i;return null}const Ae=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),Xr=e=>!et(e)&&e!==Ae;function cn(){const{caseless:e}=Xr(this)&&this||{},t={},n=(r,i)=>{const s=e&&Kr(t,i)||i;pt(t[s])&&pt(r)?t[s]=cn(t[s],r):pt(r)?t[s]=cn({},r):Fe(r)?t[s]=r.slice():t[s]=r};for(let r=0,i=arguments.length;r<i;r++)arguments[r]&&nt(arguments[r],n);return t}const eo=(e,t,n,{allOwnKeys:r}={})=>(nt(t,(i,s)=>{n&&Q(i)?e[s]=Vr(i,n):e[s]=i},{allOwnKeys:r}),e),to=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),no=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ro=(e,t,n,r)=>{let i,s,o;const a={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),s=i.length;s-- >0;)o=i[s],(!r||r(o,e,t))&&!a[o]&&(t[o]=e[o],a[o]=!0);e=n!==!1&&Rn(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},io=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},so=e=>{if(!e)return null;if(Fe(e))return e;let t=e.length;if(!Wr(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},oo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Rn(Uint8Array)),ao=(e,t)=>{const r=(e&&e[Lt]).call(e);let i;for(;(i=r.next())&&!i.done;){const s=i.value;t.call(e,s[0],s[1])}},lo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},co=ae("HTMLFormElement"),uo=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,i){return r.toUpperCase()+i}),cr=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),fo=ae("RegExp"),Yr=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};nt(n,(i,s)=>{let o;(o=t(i,s,e))!==!1&&(r[s]=o||i)}),Object.defineProperties(e,r)},po=e=>{Yr(e,(t,n)=>{if(Q(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(Q(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ho=(e,t)=>{const n={},r=i=>{i.forEach(s=>{n[s]=!0})};return Fe(e)?r(e):r(String(e).split(t)),n},mo=()=>{},go=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function vo(e){return!!(e&&Q(e.append)&&e[Gr]==="FormData"&&e[Lt])}const yo=e=>{const t=new Array(10),n=(r,i)=>{if(Rt(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[i]=r;const s=Fe(r)?[]:{};return nt(r,(o,a)=>{const c=n(o,i+1);!et(c)&&(s[a]=c)}),t[i]=void 0,s}}return r};return n(e,0)},wo=ae("AsyncFunction"),bo=e=>e&&(Rt(e)||Q(e))&&Q(e.then)&&Q(e.catch),Jr=((e,t)=>e?setImmediate:t?((n,r)=>(Ae.addEventListener("message",({source:i,data:s})=>{i===Ae&&s===n&&r.length&&r.shift()()},!1),i=>{r.push(i),Ae.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Q(Ae.postMessage)),xo=typeof queueMicrotask<"u"?queueMicrotask.bind(Ae):typeof process<"u"&&process.nextTick||Jr,So=e=>e!=null&&Q(e[Lt]),b={isArray:Fe,isArrayBuffer:Ur,isBuffer:zs,isFormData:Ws,isArrayBufferView:Fs,isString:$s,isNumber:Wr,isBoolean:js,isObject:Rt,isPlainObject:pt,isReadableStream:Xs,isRequest:Ys,isResponse:Js,isHeaders:Zs,isUndefined:et,isDate:Hs,isFile:qs,isBlob:Vs,isRegExp:fo,isFunction:Q,isStream:Us,isURLSearchParams:Ks,isTypedArray:oo,isFileList:Gs,forEach:nt,merge:cn,extend:eo,trim:Qs,stripBOM:to,inherits:no,toFlatObject:ro,kindOf:It,kindOfTest:ae,endsWith:io,toArray:so,forEachEntry:ao,matchAll:lo,isHTMLForm:co,hasOwnProperty:cr,hasOwnProp:cr,reduceDescriptors:Yr,freezeMethods:po,toObjectSet:ho,toCamelCase:uo,noop:mo,toFiniteNumber:go,findKey:Kr,global:Ae,isContextDefined:Xr,isSpecCompliantForm:vo,toJSONObject:yo,isAsyncFn:wo,isThenable:bo,setImmediate:Jr,asap:xo,isIterable:So};function D(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}b.inherits(D,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const Zr=D.prototype,Qr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Qr[e]={value:e}});Object.defineProperties(D,Qr);Object.defineProperty(Zr,"isAxiosError",{value:!0});D.from=(e,t,n,r,i,s)=>{const o=Object.create(Zr);return b.toFlatObject(e,o,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),D.call(o,e.message,t,n,r,i),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};const _o=null;function un(e){return b.isPlainObject(e)||b.isArray(e)}function ei(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function ur(e,t,n){return e?e.concat(t).map(function(i,s){return i=ei(i),!n&&s?"["+i+"]":i}).join(n?".":""):t}function Eo(e){return b.isArray(e)&&!e.some(un)}const To=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function Dt(e,t,n){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=b.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,v){return!b.isUndefined(v[y])});const r=n.metaTokens,i=n.visitor||u,s=n.dots,o=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(i))throw new TypeError("visitor must be a function");function l(f){if(f===null)return"";if(b.isDate(f))return f.toISOString();if(b.isBoolean(f))return f.toString();if(!c&&b.isBlob(f))throw new D("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(f)||b.isTypedArray(f)?c&&typeof Blob=="function"?new Blob([f]):Buffer.from(f):f}function u(f,y,v){let w=f;if(f&&!v&&typeof f=="object"){if(b.endsWith(y,"{}"))y=r?y:y.slice(0,-2),f=JSON.stringify(f);else if(b.isArray(f)&&Eo(f)||(b.isFileList(f)||b.endsWith(y,"[]"))&&(w=b.toArray(f)))return y=ei(y),w.forEach(function(m,x){!(b.isUndefined(m)||m===null)&&t.append(o===!0?ur([y],x,s):o===null?y:y+"[]",l(m))}),!1}return un(f)?!0:(t.append(ur(v,y,s),l(f)),!1)}const d=[],p=Object.assign(To,{defaultVisitor:u,convertValue:l,isVisitable:un});function g(f,y){if(!b.isUndefined(f)){if(d.indexOf(f)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(f),b.forEach(f,function(w,h){(!(b.isUndefined(w)||w===null)&&i.call(t,w,b.isString(h)?h.trim():h,y,p))===!0&&g(w,y?y.concat(h):[h])}),d.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return g(e),t}function dr(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function Dn(e,t){this._pairs=[],e&&Dt(e,this,t)}const ti=Dn.prototype;ti.append=function(t,n){this._pairs.push([t,n])};ti.toString=function(t){const n=t?function(r){return t.call(this,r,dr)}:dr;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function Co(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ni(e,t,n){if(!t)return e;const r=n&&n.encode||Co;b.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let s;if(i?s=i(t,n):s=b.isURLSearchParams(t)?t.toString():new Dn(t,n).toString(r),s){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class Oo{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(r){r!==null&&t(r)})}}const fr=Oo,ri={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ao=typeof URLSearchParams<"u"?URLSearchParams:Dn,Po=typeof FormData<"u"?FormData:null,Mo=typeof Blob<"u"?Blob:null,Lo={isBrowser:!0,classes:{URLSearchParams:Ao,FormData:Po,Blob:Mo},protocols:["http","https","file","blob","url","data"]},Nn=typeof window<"u"&&typeof document<"u",dn=typeof navigator=="object"&&navigator||void 0,Io=Nn&&(!dn||["ReactNative","NativeScript","NS"].indexOf(dn.product)<0),ko=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),Ro=Nn&&window.location.href||"http://localhost",Do=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Nn,hasStandardBrowserEnv:Io,hasStandardBrowserWebWorkerEnv:ko,navigator:dn,origin:Ro},Symbol.toStringTag,{value:"Module"})),K={...Do,...Lo};function No(e,t){return Dt(e,new K.classes.URLSearchParams,Object.assign({visitor:function(n,r,i,s){return K.isNode&&b.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function Bo(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function zo(e){const t={},n=Object.keys(e);let r;const i=n.length;let s;for(r=0;r<i;r++)s=n[r],t[s]=e[s];return t}function ii(e){function t(n,r,i,s){let o=n[s++];if(o==="__proto__")return!0;const a=Number.isFinite(+o),c=s>=n.length;return o=!o&&b.isArray(i)?i.length:o,c?(b.hasOwnProp(i,o)?i[o]=[i[o],r]:i[o]=r,!a):((!i[o]||!b.isObject(i[o]))&&(i[o]=[]),t(n,r,i[o],s)&&b.isArray(i[o])&&(i[o]=zo(i[o])),!a)}if(b.isFormData(e)&&b.isFunction(e.entries)){const n={};return b.forEachEntry(e,(r,i)=>{t(Bo(r),i,n,0)}),n}return null}function Fo(e,t,n){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Bn={transitional:ri,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",i=r.indexOf("application/json")>-1,s=b.isObject(t);if(s&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return i?JSON.stringify(ii(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return No(t,this.formSerializer).toString();if((a=b.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Dt(a?{"files[]":t}:t,c&&new c,this.formSerializer)}}return s||i?(n.setContentType("application/json",!1),Fo(t)):t}],transformResponse:[function(t){const n=this.transitional||Bn.transitional,r=n&&n.forcedJSONParsing,i=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(r&&!this.responseType||i)){const o=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(a){if(o)throw a.name==="SyntaxError"?D.from(a,D.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:K.classes.FormData,Blob:K.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{Bn.headers[e]={}});const zn=Bn,$o=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),jo=e=>{const t={};let n,r,i;return e&&e.split(`
`).forEach(function(o){i=o.indexOf(":"),n=o.substring(0,i).trim().toLowerCase(),r=o.substring(i+1).trim(),!(!n||t[n]&&$o[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},pr=Symbol("internals");function Ue(e){return e&&String(e).trim().toLowerCase()}function ht(e){return e===!1||e==null?e:b.isArray(e)?e.map(ht):String(e)}function Ho(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const qo=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Wt(e,t,n,r,i){if(b.isFunction(r))return r.call(this,t,n);if(i&&(t=n),!!b.isString(t)){if(b.isString(r))return t.indexOf(r)!==-1;if(b.isRegExp(r))return r.test(t)}}function Vo(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Go(e,t){const n=b.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(i,s,o){return this[r].call(this,t,i,s,o)},configurable:!0})})}class Nt{constructor(t){t&&this.set(t)}set(t,n,r){const i=this;function s(a,c,l){const u=Ue(c);if(!u)throw new Error("header name must be a non-empty string");const d=b.findKey(i,u);(!d||i[d]===void 0||l===!0||l===void 0&&i[d]!==!1)&&(i[d||c]=ht(a))}const o=(a,c)=>b.forEach(a,(l,u)=>s(l,u,c));if(b.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(b.isString(t)&&(t=t.trim())&&!qo(t))o(jo(t),n);else if(b.isObject(t)&&b.isIterable(t)){let a={},c,l;for(const u of t){if(!b.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[l=u[0]]=(c=a[l])?b.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}o(a,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=Ue(t),t){const r=b.findKey(this,t);if(r){const i=this[r];if(!n)return i;if(n===!0)return Ho(i);if(b.isFunction(n))return n.call(this,i,r);if(b.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ue(t),t){const r=b.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||Wt(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let i=!1;function s(o){if(o=Ue(o),o){const a=b.findKey(r,o);a&&(!n||Wt(r,r[a],a,n))&&(delete r[a],i=!0)}}return b.isArray(t)?t.forEach(s):s(t),i}clear(t){const n=Object.keys(this);let r=n.length,i=!1;for(;r--;){const s=n[r];(!t||Wt(this,this[s],s,t,!0))&&(delete this[s],i=!0)}return i}normalize(t){const n=this,r={};return b.forEach(this,(i,s)=>{const o=b.findKey(r,s);if(o){n[o]=ht(i),delete n[s];return}const a=t?Vo(s):String(s).trim();a!==s&&delete n[s],n[a]=ht(i),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return b.forEach(this,(r,i)=>{r!=null&&r!==!1&&(n[i]=t&&b.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(i=>r.set(i)),r}static accessor(t){const r=(this[pr]=this[pr]={accessors:{}}).accessors,i=this.prototype;function s(o){const a=Ue(o);r[a]||(Go(i,o),r[a]=!0)}return b.isArray(t)?t.forEach(s):s(t),this}}Nt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(Nt.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});b.freezeMethods(Nt);const se=Nt;function Kt(e,t){const n=this||zn,r=t||n,i=se.from(r.headers);let s=r.data;return b.forEach(e,function(a){s=a.call(n,s,i.normalize(),t?t.status:void 0)}),i.normalize(),s}function si(e){return!!(e&&e.__CANCEL__)}function $e(e,t,n){D.call(this,e??"canceled",D.ERR_CANCELED,t,n),this.name="CanceledError"}b.inherits($e,D,{__CANCEL__:!0});function oi(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new D("Request failed with status code "+n.status,[D.ERR_BAD_REQUEST,D.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Uo(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Wo(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i=0,s=0,o;return t=t!==void 0?t:1e3,function(c){const l=Date.now(),u=r[s];o||(o=l),n[i]=c,r[i]=l;let d=s,p=0;for(;d!==i;)p+=n[d++],d=d%e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),l-o<t)return;const g=u&&l-u;return g?Math.round(p*1e3/g):void 0}}function Ko(e,t){let n=0,r=1e3/t,i,s;const o=(l,u=Date.now())=>{n=u,i=null,s&&(clearTimeout(s),s=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),d=u-n;d>=r?o(l,u):(i=l,s||(s=setTimeout(()=>{s=null,o(i)},r-d)))},()=>i&&o(i)]}const bt=(e,t,n=3)=>{let r=0;const i=Wo(50,250);return Ko(s=>{const o=s.loaded,a=s.lengthComputable?s.total:void 0,c=o-r,l=i(c),u=o<=a;r=o;const d={loaded:o,total:a,progress:a?o/a:void 0,bytes:c,rate:l||void 0,estimated:l&&a&&u?(a-o)/l:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},n)},hr=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},mr=e=>(...t)=>b.asap(()=>e(...t)),Xo=K.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,K.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(K.origin),K.navigator&&/(msie|trident)/i.test(K.navigator.userAgent)):()=>!0,Yo=K.hasStandardBrowserEnv?{write(e,t,n,r,i,s){const o=[e+"="+encodeURIComponent(t)];b.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),b.isString(r)&&o.push("path="+r),b.isString(i)&&o.push("domain="+i),s===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Jo(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Zo(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function ai(e,t,n){let r=!Jo(t);return e&&(r||n==!1)?Zo(e,t):t}const gr=e=>e instanceof se?{...e}:e;function Re(e,t){t=t||{};const n={};function r(l,u,d,p){return b.isPlainObject(l)&&b.isPlainObject(u)?b.merge.call({caseless:p},l,u):b.isPlainObject(u)?b.merge({},u):b.isArray(u)?u.slice():u}function i(l,u,d,p){if(b.isUndefined(u)){if(!b.isUndefined(l))return r(void 0,l,d,p)}else return r(l,u,d,p)}function s(l,u){if(!b.isUndefined(u))return r(void 0,u)}function o(l,u){if(b.isUndefined(u)){if(!b.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function a(l,u,d){if(d in t)return r(l,u);if(d in e)return r(void 0,l)}const c={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(l,u,d)=>i(gr(l),gr(u),d,!0)};return b.forEach(Object.keys(Object.assign({},e,t)),function(u){const d=c[u]||i,p=d(e[u],t[u],u);b.isUndefined(p)&&d!==a||(n[u]=p)}),n}const li=e=>{const t=Re({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:i,xsrfCookieName:s,headers:o,auth:a}=t;t.headers=o=se.from(o),t.url=ni(ai(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&o.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(b.isFormData(n)){if(K.hasStandardBrowserEnv||K.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((c=o.getContentType())!==!1){const[l,...u]=c?c.split(";").map(d=>d.trim()).filter(Boolean):[];o.setContentType([l||"multipart/form-data",...u].join("; "))}}if(K.hasStandardBrowserEnv&&(r&&b.isFunction(r)&&(r=r(t)),r||r!==!1&&Xo(t.url))){const l=i&&s&&Yo.read(s);l&&o.set(i,l)}return t},Qo=typeof XMLHttpRequest<"u",ea=Qo&&function(e){return new Promise(function(n,r){const i=li(e);let s=i.data;const o=se.from(i.headers).normalize();let{responseType:a,onUploadProgress:c,onDownloadProgress:l}=i,u,d,p,g,f;function y(){g&&g(),f&&f(),i.cancelToken&&i.cancelToken.unsubscribe(u),i.signal&&i.signal.removeEventListener("abort",u)}let v=new XMLHttpRequest;v.open(i.method.toUpperCase(),i.url,!0),v.timeout=i.timeout;function w(){if(!v)return;const m=se.from("getAllResponseHeaders"in v&&v.getAllResponseHeaders()),_={data:!a||a==="text"||a==="json"?v.responseText:v.response,status:v.status,statusText:v.statusText,headers:m,config:e,request:v};oi(function(M){n(M),y()},function(M){r(M),y()},_),v=null}"onloadend"in v?v.onloadend=w:v.onreadystatechange=function(){!v||v.readyState!==4||v.status===0&&!(v.responseURL&&v.responseURL.indexOf("file:")===0)||setTimeout(w)},v.onabort=function(){v&&(r(new D("Request aborted",D.ECONNABORTED,e,v)),v=null)},v.onerror=function(){r(new D("Network Error",D.ERR_NETWORK,e,v)),v=null},v.ontimeout=function(){let x=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const _=i.transitional||ri;i.timeoutErrorMessage&&(x=i.timeoutErrorMessage),r(new D(x,_.clarifyTimeoutError?D.ETIMEDOUT:D.ECONNABORTED,e,v)),v=null},s===void 0&&o.setContentType(null),"setRequestHeader"in v&&b.forEach(o.toJSON(),function(x,_){v.setRequestHeader(_,x)}),b.isUndefined(i.withCredentials)||(v.withCredentials=!!i.withCredentials),a&&a!=="json"&&(v.responseType=i.responseType),l&&([p,f]=bt(l,!0),v.addEventListener("progress",p)),c&&v.upload&&([d,g]=bt(c),v.upload.addEventListener("progress",d),v.upload.addEventListener("loadend",g)),(i.cancelToken||i.signal)&&(u=m=>{v&&(r(!m||m.type?new $e(null,e,v):m),v.abort(),v=null)},i.cancelToken&&i.cancelToken.subscribe(u),i.signal&&(i.signal.aborted?u():i.signal.addEventListener("abort",u)));const h=Uo(i.url);if(h&&K.protocols.indexOf(h)===-1){r(new D("Unsupported protocol "+h+":",D.ERR_BAD_REQUEST,e));return}v.send(s||null)})},ta=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,i;const s=function(l){if(!i){i=!0,a();const u=l instanceof Error?l:this.reason;r.abort(u instanceof D?u:new $e(u instanceof Error?u.message:u))}};let o=t&&setTimeout(()=>{o=null,s(new D(`timeout ${t} of ms exceeded`,D.ETIMEDOUT))},t);const a=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(s):l.removeEventListener("abort",s)}),e=null)};e.forEach(l=>l.addEventListener("abort",s));const{signal:c}=r;return c.unsubscribe=()=>b.asap(a),c}},na=ta,ra=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let r=0,i;for(;r<n;)i=r+t,yield e.slice(r,i),r=i},ia=async function*(e,t){for await(const n of sa(e))yield*ra(n,t)},sa=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},vr=(e,t,n,r)=>{const i=ia(e,t);let s=0,o,a=c=>{o||(o=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:l,value:u}=await i.next();if(l){a(),c.close();return}let d=u.byteLength;if(n){let p=s+=d;n(p)}c.enqueue(new Uint8Array(u))}catch(l){throw a(l),l}},cancel(c){return a(c),i.return()}},{highWaterMark:2})},Bt=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ci=Bt&&typeof ReadableStream=="function",oa=Bt&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),ui=(e,...t)=>{try{return!!e(...t)}catch{return!1}},aa=ci&&ui(()=>{let e=!1;const t=new Request(K.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),yr=64*1024,fn=ci&&ui(()=>b.isReadableStream(new Response("").body)),xt={stream:fn&&(e=>e.body)};Bt&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!xt[t]&&(xt[t]=b.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new D(`Response type '${t}' is not supported`,D.ERR_NOT_SUPPORT,r)})})})(new Response);const la=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(K.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await oa(e)).byteLength},ca=async(e,t)=>{const n=b.toFiniteNumber(e.getContentLength());return n??la(t)},ua=Bt&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:s,timeout:o,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:p}=li(e);l=l?(l+"").toLowerCase():"text";let g=na([i,s&&s.toAbortSignal()],o),f;const y=g&&g.unsubscribe&&(()=>{g.unsubscribe()});let v;try{if(c&&aa&&n!=="get"&&n!=="head"&&(v=await ca(u,r))!==0){let _=new Request(t,{method:"POST",body:r,duplex:"half"}),T;if(b.isFormData(r)&&(T=_.headers.get("content-type"))&&u.setContentType(T),_.body){const[M,O]=hr(v,bt(mr(c)));r=vr(_.body,yr,M,O)}}b.isString(d)||(d=d?"include":"omit");const w="credentials"in Request.prototype;f=new Request(t,{...p,signal:g,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:w?d:void 0});let h=await fetch(f,p);const m=fn&&(l==="stream"||l==="response");if(fn&&(a||m&&y)){const _={};["status","statusText","headers"].forEach(L=>{_[L]=h[L]});const T=b.toFiniteNumber(h.headers.get("content-length")),[M,O]=a&&hr(T,bt(mr(a),!0))||[];h=new Response(vr(h.body,yr,M,()=>{O&&O(),y&&y()}),_)}l=l||"text";let x=await xt[b.findKey(xt,l)||"text"](h,e);return!m&&y&&y(),await new Promise((_,T)=>{oi(_,T,{data:x,headers:se.from(h.headers),status:h.status,statusText:h.statusText,config:e,request:f})})}catch(w){throw y&&y(),w&&w.name==="TypeError"&&/Load failed|fetch/i.test(w.message)?Object.assign(new D("Network Error",D.ERR_NETWORK,e,f),{cause:w.cause||w}):D.from(w,w&&w.code,e,f)}}),pn={http:_o,xhr:ea,fetch:ua};b.forEach(pn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const wr=e=>`- ${e}`,da=e=>b.isFunction(e)||e===null||e===!1,di={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let s=0;s<t;s++){n=e[s];let o;if(r=n,!da(n)&&(r=pn[(o=String(n)).toLowerCase()],r===void 0))throw new D(`Unknown adapter '${o}'`);if(r)break;i[o||"#"+s]=r}if(!r){const s=Object.entries(i).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build"));let o=t?s.length>1?`since :
`+s.map(wr).join(`
`):" "+wr(s[0]):"as no adapter specified";throw new D("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return r},adapters:pn};function Xt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new $e(null,e)}function br(e){return Xt(e),e.headers=se.from(e.headers),e.data=Kt.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),di.getAdapter(e.adapter||zn.adapter)(e).then(function(r){return Xt(e),r.data=Kt.call(e,e.transformResponse,r),r.headers=se.from(r.headers),r},function(r){return si(r)||(Xt(e),r&&r.response&&(r.response.data=Kt.call(e,e.transformResponse,r.response),r.response.headers=se.from(r.response.headers))),Promise.reject(r)})}const fi="1.10.0",zt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{zt[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const xr={};zt.transitional=function(t,n,r){function i(s,o){return"[Axios v"+fi+"] Transitional option '"+s+"'"+o+(r?". "+r:"")}return(s,o,a)=>{if(t===!1)throw new D(i(o," has been removed"+(n?" in "+n:"")),D.ERR_DEPRECATED);return n&&!xr[o]&&(xr[o]=!0,console.warn(i(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,o,a):!0}};zt.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function fa(e,t,n){if(typeof e!="object")throw new D("options must be an object",D.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const s=r[i],o=t[s];if(o){const a=e[s],c=a===void 0||o(a,s,e);if(c!==!0)throw new D("option "+s+" must be "+c,D.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new D("Unknown option "+s,D.ERR_BAD_OPTION)}}const mt={assertOptions:fa,validators:zt},pe=mt.validators;class St{constructor(t){this.defaults=t||{},this.interceptors={request:new fr,response:new fr}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const s=i.stack?i.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Re(this.defaults,n);const{transitional:r,paramsSerializer:i,headers:s}=n;r!==void 0&&mt.assertOptions(r,{silentJSONParsing:pe.transitional(pe.boolean),forcedJSONParsing:pe.transitional(pe.boolean),clarifyTimeoutError:pe.transitional(pe.boolean)},!1),i!=null&&(b.isFunction(i)?n.paramsSerializer={serialize:i}:mt.assertOptions(i,{encode:pe.function,serialize:pe.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),mt.assertOptions(n,{baseUrl:pe.spelling("baseURL"),withXsrfToken:pe.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=s&&b.merge(s.common,s[n.method]);s&&b.forEach(["delete","get","head","post","put","patch","common"],f=>{delete s[f]}),n.headers=se.concat(o,s);const a=[];let c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(c=c&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,d=0,p;if(!c){const f=[br.bind(this),void 0];for(f.unshift.apply(f,a),f.push.apply(f,l),p=f.length,u=Promise.resolve(n);d<p;)u=u.then(f[d++],f[d++]);return u}p=a.length;let g=n;for(d=0;d<p;){const f=a[d++],y=a[d++];try{g=f(g)}catch(v){y.call(this,v);break}}try{u=br.call(this,g)}catch(f){return Promise.reject(f)}for(d=0,p=l.length;d<p;)u=u.then(l[d++],l[d++]);return u}getUri(t){t=Re(this.defaults,t);const n=ai(t.baseURL,t.url,t.allowAbsoluteUrls);return ni(n,t.params,t.paramsSerializer)}}b.forEach(["delete","get","head","options"],function(t){St.prototype[t]=function(n,r){return this.request(Re(r||{},{method:t,url:n,data:(r||{}).data}))}});b.forEach(["post","put","patch"],function(t){function n(r){return function(s,o,a){return this.request(Re(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:o}))}}St.prototype[t]=n(),St.prototype[t+"Form"]=n(!0)});const gt=St;class Fn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(i=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](i);r._listeners=null}),this.promise.then=i=>{let s;const o=new Promise(a=>{r.subscribe(a),s=a}).then(i);return o.cancel=function(){r.unsubscribe(s)},o},t(function(s,o,a){r.reason||(r.reason=new $e(s,o,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Fn(function(i){t=i}),cancel:t}}}const pa=Fn;function ha(e){return function(n){return e.apply(null,n)}}function ma(e){return b.isObject(e)&&e.isAxiosError===!0}const hn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(hn).forEach(([e,t])=>{hn[t]=e});const ga=hn;function pi(e){const t=new gt(e),n=Vr(gt.prototype.request,t);return b.extend(n,gt.prototype,t,{allOwnKeys:!0}),b.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return pi(Re(e,i))},n}const q=pi(zn);q.Axios=gt;q.CanceledError=$e;q.CancelToken=pa;q.isCancel=si;q.VERSION=fi;q.toFormData=Dt;q.AxiosError=D;q.Cancel=q.CanceledError;q.all=function(t){return Promise.all(t)};q.spread=ha;q.isAxiosError=ma;q.mergeConfig=Re;q.AxiosHeaders=se;q.formToJSON=e=>ii(b.isHTMLForm(e)?new FormData(e):e);q.getAdapter=di.getAdapter;q.HttpStatusCode=ga;q.default=q;const va=q;window.axios=va;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";var mn=!1,gn=!1,Me=[],vn=-1;function ya(e){wa(e)}function wa(e){Me.includes(e)||Me.push(e),xa()}function ba(e){let t=Me.indexOf(e);t!==-1&&t>vn&&Me.splice(t,1)}function xa(){!gn&&!mn&&(mn=!0,queueMicrotask(Sa))}function Sa(){mn=!1,gn=!0;for(let e=0;e<Me.length;e++)Me[e](),vn=e;Me.length=0,vn=-1,gn=!1}var je,Ne,He,hi,yn=!0;function _a(e){yn=!1,e(),yn=!0}function Ea(e){je=e.reactive,He=e.release,Ne=t=>e.effect(t,{scheduler:n=>{yn?ya(n):n()}}),hi=e.raw}function Sr(e){Ne=e}function Ta(e){let t=()=>{};return[r=>{let i=Ne(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(s=>s())}),e._x_effects.add(i),t=()=>{i!==void 0&&(e._x_effects.delete(i),He(i))},i},()=>{t()}]}function mi(e,t){let n=!0,r,i=Ne(()=>{let s=e();JSON.stringify(s),n?r=s:queueMicrotask(()=>{t(s,r),r=s}),n=!1});return()=>He(i)}var gi=[],vi=[],yi=[];function Ca(e){yi.push(e)}function $n(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,vi.push(t))}function wi(e){gi.push(e)}function bi(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function xi(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(i=>i()),delete e._x_attributeCleanups[n])})}function Oa(e){var t,n;for((t=e._x_effects)==null||t.forEach(ba);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var jn=new MutationObserver(Gn),Hn=!1;function qn(){jn.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Hn=!0}function Si(){Aa(),jn.disconnect(),Hn=!1}var We=[];function Aa(){let e=jn.takeRecords();We.push(()=>e.length>0&&Gn(e));let t=We.length;queueMicrotask(()=>{if(We.length===t)for(;We.length>0;)We.shift()()})}function j(e){if(!Hn)return e();Si();let t=e();return qn(),t}var Vn=!1,_t=[];function Pa(){Vn=!0}function Ma(){Vn=!1,Gn(_t),_t=[]}function Gn(e){if(Vn){_t=_t.concat(e);return}let t=[],n=new Set,r=new Map,i=new Map;for(let s=0;s<e.length;s++)if(!e[s].target._x_ignoreMutationObserver&&(e[s].type==="childList"&&(e[s].removedNodes.forEach(o=>{o.nodeType===1&&o._x_marker&&n.add(o)}),e[s].addedNodes.forEach(o=>{if(o.nodeType===1){if(n.has(o)){n.delete(o);return}o._x_marker||t.push(o)}})),e[s].type==="attributes")){let o=e[s].target,a=e[s].attributeName,c=e[s].oldValue,l=()=>{r.has(o)||r.set(o,[]),r.get(o).push({name:a,value:o.getAttribute(a)})},u=()=>{i.has(o)||i.set(o,[]),i.get(o).push(a)};o.hasAttribute(a)&&c===null?l():o.hasAttribute(a)?(u(),l()):u()}i.forEach((s,o)=>{xi(o,s)}),r.forEach((s,o)=>{gi.forEach(a=>a(o,s))});for(let s of n)t.some(o=>o.contains(s))||vi.forEach(o=>o(s));for(let s of t)s.isConnected&&yi.forEach(o=>o(s));t=null,n=null,r=null,i=null}function _i(e){return it(Be(e))}function rt(e,t,n){return e._x_dataStack=[t,...Be(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function Be(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?Be(e.host):e.parentNode?Be(e.parentNode):[]}function it(e){return new Proxy({objects:e},La)}var La={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?Ia:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const i=e.find(o=>Object.prototype.hasOwnProperty.call(o,t))||e[e.length-1],s=Object.getOwnPropertyDescriptor(i,t);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(r,n)||!0:Reflect.set(i,t,n)}};function Ia(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function Ei(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,i="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([s,{value:o,enumerable:a}])=>{if(a===!1||o===void 0||typeof o=="object"&&o!==null&&o.__v_skip)return;let c=i===""?s:`${i}.${s}`;typeof o=="object"&&o!==null&&o._x_interceptor?r[s]=o.initialize(e,c,s):t(o)&&o!==r&&!(o instanceof Element)&&n(o,c)})};return n(e)}function Ti(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,i,s){return e(this.initialValue,()=>ka(r,i),o=>wn(r,i,o),i,s)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let i=n.initialize.bind(n);n.initialize=(s,o,a)=>{let c=r.initialize(s,o,a);return n.initialValue=c,i(s,o,a)}}else n.initialValue=r;return n}}function ka(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function wn(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),wn(e[t[0]],t.slice(1),n)}}var Ci={};function le(e,t){Ci[e]=t}function bn(e,t){let n=Ra(t);return Object.entries(Ci).forEach(([r,i])=>{Object.defineProperty(e,`$${r}`,{get(){return i(t,n)},enumerable:!1})}),e}function Ra(e){let[t,n]=Ii(e),r={interceptor:Ti,...t};return $n(e,n),r}function Da(e,t,n,...r){try{return n(...r)}catch(i){tt(i,e,t)}}function tt(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var vt=!0;function Oi(e){let t=vt;vt=!1;let n=e();return vt=t,n}function Le(e,t,n={}){let r;return X(e,t)(i=>r=i,n),r}function X(...e){return Ai(...e)}var Ai=Pi;function Na(e){Ai=e}function Pi(e,t){let n={};bn(n,e);let r=[n,...Be(e)],i=typeof t=="function"?Ba(r,t):Fa(r,t,e);return Da.bind(null,e,t,i)}function Ba(e,t){return(n=()=>{},{scope:r={},params:i=[]}={})=>{let s=t.apply(it([r,...e]),i);Et(n,s)}}var Yt={};function za(e,t){if(Yt[e])return Yt[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,s=(()=>{try{let o=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(o,"name",{value:`[Alpine] ${e}`}),o}catch(o){return tt(o,t,e),Promise.resolve()}})();return Yt[e]=s,s}function Fa(e,t,n){let r=za(t,n);return(i=()=>{},{scope:s={},params:o=[]}={})=>{r.result=void 0,r.finished=!1;let a=it([s,...e]);if(typeof r=="function"){let c=r(r,a).catch(l=>tt(l,n,t));r.finished?(Et(i,r.result,a,o,n),r.result=void 0):c.then(l=>{Et(i,l,a,o,n)}).catch(l=>tt(l,n,t)).finally(()=>r.result=void 0)}}}function Et(e,t,n,r,i){if(vt&&typeof t=="function"){let s=t.apply(n,r);s instanceof Promise?s.then(o=>Et(e,o,n,r)).catch(o=>tt(o,i,t)):e(s)}else typeof t=="object"&&t instanceof Promise?t.then(s=>e(s)):e(t)}var Un="x-";function qe(e=""){return Un+e}function $a(e){Un=e}var Tt={};function V(e,t){return Tt[e]=t,{before(n){if(!Tt[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=Pe.indexOf(n);Pe.splice(r>=0?r:Pe.indexOf("DEFAULT"),0,e)}}}function ja(e){return Object.keys(Tt).includes(e)}function Wn(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let s=Object.entries(e._x_virtualDirectives).map(([a,c])=>({name:a,value:c})),o=Mi(s);s=s.map(a=>o.find(c=>c.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(s)}let r={};return t.map(Di((s,o)=>r[s]=o)).filter(Bi).map(Va(r,n)).sort(Ga).map(s=>qa(e,s))}function Mi(e){return Array.from(e).map(Di()).filter(t=>!Bi(t))}var xn=!1,Je=new Map,Li=Symbol();function Ha(e){xn=!0;let t=Symbol();Li=t,Je.set(t,[]);let n=()=>{for(;Je.get(t).length;)Je.get(t).shift()();Je.delete(t)},r=()=>{xn=!1,n()};e(n),r()}function Ii(e){let t=[],n=a=>t.push(a),[r,i]=Ta(e);return t.push(i),[{Alpine:st,effect:r,cleanup:n,evaluateLater:X.bind(X,e),evaluate:Le.bind(Le,e)},()=>t.forEach(a=>a())]}function qa(e,t){let n=()=>{},r=Tt[t.type]||n,[i,s]=Ii(e);bi(e,t.original,s);let o=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,i),r=r.bind(r,e,t,i),xn?Je.get(Li).push(r):r())};return o.runCleanups=s,o}var ki=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),Ri=e=>e;function Di(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:i}=Ni.reduce((s,o)=>o(s),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:i}}}var Ni=[];function Kn(e){Ni.push(e)}function Bi({name:e}){return zi().test(e)}var zi=()=>new RegExp(`^${Un}([^:^.]+)\\b`);function Va(e,t){return({name:n,value:r})=>{let i=n.match(zi()),s=n.match(/:([a-zA-Z0-9\-_:]+)/),o=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[n]||n;return{type:i?i[1]:null,value:s?s[1]:null,modifiers:o.map(c=>c.replace(".","")),expression:r,original:a}}}var Sn="DEFAULT",Pe=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Sn,"teleport"];function Ga(e,t){let n=Pe.indexOf(e.type)===-1?Sn:e.type,r=Pe.indexOf(t.type)===-1?Sn:t.type;return Pe.indexOf(n)-Pe.indexOf(r)}function Ze(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function De(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(i=>De(i,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)De(r,t),r=r.nextElementSibling}function re(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var _r=!1;function Ua(){_r&&re("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),_r=!0,document.body||re("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Ze(document,"alpine:init"),Ze(document,"alpine:initializing"),qn(),Ca(t=>ye(t,De)),$n(t=>Ge(t)),wi((t,n)=>{Wn(t,n).forEach(r=>r())});let e=t=>!Ft(t.parentElement,!0);Array.from(document.querySelectorAll(ji().join(","))).filter(e).forEach(t=>{ye(t)}),Ze(document,"alpine:initialized"),setTimeout(()=>{Ya()})}var Xn=[],Fi=[];function $i(){return Xn.map(e=>e())}function ji(){return Xn.concat(Fi).map(e=>e())}function Hi(e){Xn.push(e)}function qi(e){Fi.push(e)}function Ft(e,t=!1){return Ve(e,n=>{if((t?ji():$i()).some(i=>n.matches(i)))return!0})}function Ve(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return Ve(e.parentElement,t)}}function Wa(e){return $i().some(t=>e.matches(t))}var Vi=[];function Ka(e){Vi.push(e)}var Xa=1;function ye(e,t=De,n=()=>{}){Ve(e,r=>r._x_ignore)||Ha(()=>{t(e,(r,i)=>{r._x_marker||(n(r,i),Vi.forEach(s=>s(r,i)),Wn(r,r.attributes).forEach(s=>s()),r._x_ignore||(r._x_marker=Xa++),r._x_ignore&&i())})})}function Ge(e,t=De){t(e,n=>{Oa(n),xi(n),delete n._x_marker})}function Ya(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{ja(n)||r.some(i=>{if(document.querySelector(i))return re(`found "${i}", but missing ${t} plugin`),!0})})}var _n=[],Yn=!1;function Jn(e=()=>{}){return queueMicrotask(()=>{Yn||setTimeout(()=>{En()})}),new Promise(t=>{_n.push(()=>{e(),t()})})}function En(){for(Yn=!1;_n.length;)_n.shift()()}function Ja(){Yn=!0}function Zn(e,t){return Array.isArray(t)?Er(e,t.join(" ")):typeof t=="object"&&t!==null?Za(e,t):typeof t=="function"?Zn(e,t()):Er(e,t)}function Er(e,t){let n=i=>i.split(" ").filter(s=>!e.classList.contains(s)).filter(Boolean),r=i=>(e.classList.add(...i),()=>{e.classList.remove(...i)});return t=t===!0?t="":t||"",r(n(t))}function Za(e,t){let n=a=>a.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([a,c])=>c?n(a):!1).filter(Boolean),i=Object.entries(t).flatMap(([a,c])=>c?!1:n(a)).filter(Boolean),s=[],o=[];return i.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),o.push(a))}),r.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),s.push(a))}),()=>{o.forEach(a=>e.classList.add(a)),s.forEach(a=>e.classList.remove(a))}}function $t(e,t){return typeof t=="object"&&t!==null?Qa(e,t):el(e,t)}function Qa(e,t){let n={};return Object.entries(t).forEach(([r,i])=>{n[r]=e.style[r],r.startsWith("--")||(r=tl(r)),e.style.setProperty(r,i)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{$t(e,n)}}function el(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function tl(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Tn(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}V("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:i})=>{typeof r=="function"&&(r=i(r)),r!==!1&&(!r||typeof r=="boolean"?rl(e,n,t):nl(e,r,t))});function nl(e,t,n){Gi(e,Zn,""),{enter:i=>{e._x_transition.enter.during=i},"enter-start":i=>{e._x_transition.enter.start=i},"enter-end":i=>{e._x_transition.enter.end=i},leave:i=>{e._x_transition.leave.during=i},"leave-start":i=>{e._x_transition.leave.start=i},"leave-end":i=>{e._x_transition.leave.end=i}}[n](t)}function rl(e,t,n){Gi(e,$t);let r=!t.includes("in")&&!t.includes("out")&&!n,i=r||t.includes("in")||["enter"].includes(n),s=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((w,h)=>h<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((w,h)=>h>t.indexOf("out")));let o=!t.includes("opacity")&&!t.includes("scale"),a=o||t.includes("opacity"),c=o||t.includes("scale"),l=a?0:1,u=c?Ke(t,"scale",95)/100:1,d=Ke(t,"delay",0)/1e3,p=Ke(t,"origin","center"),g="opacity, transform",f=Ke(t,"duration",150)/1e3,y=Ke(t,"duration",75)/1e3,v="cubic-bezier(0.4, 0.0, 0.2, 1)";i&&(e._x_transition.enter.during={transformOrigin:p,transitionDelay:`${d}s`,transitionProperty:g,transitionDuration:`${f}s`,transitionTimingFunction:v},e._x_transition.enter.start={opacity:l,transform:`scale(${u})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(e._x_transition.leave.during={transformOrigin:p,transitionDelay:`${d}s`,transitionProperty:g,transitionDuration:`${y}s`,transitionTimingFunction:v},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:l,transform:`scale(${u})`})}function Gi(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},i=()=>{}){Cn(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,i)},out(r=()=>{},i=()=>{}){Cn(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,i)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const i=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let s=()=>i(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):s():e._x_transition?e._x_transition.in(n):s();return}e._x_hidePromise=e._x_transition?new Promise((o,a)=>{e._x_transition.out(()=>{},()=>o(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let o=Ui(e);o?(o._x_hideChildren||(o._x_hideChildren=[]),o._x_hideChildren.push(e)):i(()=>{let a=c=>{let l=Promise.all([c._x_hidePromise,...(c._x_hideChildren||[]).map(a)]).then(([u])=>u==null?void 0:u());return delete c._x_hidePromise,delete c._x_hideChildren,l};a(e).catch(c=>{if(!c.isFromCancelledTransition)throw c})})})};function Ui(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:Ui(t)}function Cn(e,t,{during:n,start:r,end:i}={},s=()=>{},o=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(i).length===0){s(),o();return}let a,c,l;il(e,{start(){a=t(e,r)},during(){c=t(e,n)},before:s,end(){a(),l=t(e,i)},after:o,cleanup(){c(),l()}})}function il(e,t){let n,r,i,s=Tn(()=>{j(()=>{n=!0,r||t.before(),i||(t.end(),En()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(o){this.beforeCancels.push(o)},cancel:Tn(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},j(()=>{t.start(),t.during()}),Ja(),requestAnimationFrame(()=>{if(n)return;let o=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;o===0&&(o=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),j(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(j(()=>{t.end()}),En(),setTimeout(e._x_transitioning.finish,o+a),i=!0)})})}function Ke(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var Ee=!1;function Ce(e,t=()=>{}){return(...n)=>Ee?t(...n):e(...n)}function sl(e){return(...t)=>Ee&&e(...t)}var Wi=[];function jt(e){Wi.push(e)}function ol(e,t){Wi.forEach(n=>n(e,t)),Ee=!0,Ki(()=>{ye(t,(n,r)=>{r(n,()=>{})})}),Ee=!1}var On=!1;function al(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),Ee=!0,On=!0,Ki(()=>{ll(t)}),Ee=!1,On=!1}function ll(e){let t=!1;ye(e,(r,i)=>{De(r,(s,o)=>{if(t&&Wa(s))return o();t=!0,i(s,o)})})}function Ki(e){let t=Ne;Sr((n,r)=>{let i=t(n);return He(i),()=>{}}),e(),Sr(t)}function Xi(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=je({})),e._x_bindings[t]=n,t=r.includes("camel")?gl(t):t,t){case"value":cl(e,n);break;case"style":dl(e,n);break;case"class":ul(e,n);break;case"selected":case"checked":fl(e,t,n);break;default:Yi(e,t,n);break}}function cl(e,t){if(Qi(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=yt(e.value)===t:e.checked=Tr(e.value,t));else if(Qn(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>Tr(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")ml(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function ul(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=Zn(e,t)}function dl(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=$t(e,t)}function fl(e,t,n){Yi(e,t,n),hl(e,t,n)}function Yi(e,t,n){[null,void 0,!1].includes(n)&&yl(t)?e.removeAttribute(t):(Ji(t)&&(n=t),pl(e,t,n))}function pl(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function hl(e,t,n){e[t]!==n&&(e[t]=n)}function ml(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function gl(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function Tr(e,t){return e==t}function yt(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var vl=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function Ji(e){return vl.has(e)}function yl(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function wl(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:Zi(e,t,n)}function bl(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let i=e._x_inlineBindings[t];return i.extract=r,Oi(()=>Le(e,i.expression))}return Zi(e,t,n)}function Zi(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:Ji(t)?!![t,"true"].includes(r):r}function Qn(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function Qi(e){return e.type==="radio"||e.localName==="ui-radio"}function es(e,t){var n;return function(){var r=this,i=arguments,s=function(){n=null,e.apply(r,i)};clearTimeout(n),n=setTimeout(s,t)}}function ts(e,t){let n;return function(){let r=this,i=arguments;n||(e.apply(r,i),n=!0,setTimeout(()=>n=!1,t))}}function ns({get:e,set:t},{get:n,set:r}){let i=!0,s,o=Ne(()=>{let a=e(),c=n();if(i)r(Jt(a)),i=!1;else{let l=JSON.stringify(a),u=JSON.stringify(c);l!==s?r(Jt(a)):l!==u&&t(Jt(c))}s=JSON.stringify(e()),JSON.stringify(n())});return()=>{He(o)}}function Jt(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function xl(e){(Array.isArray(e)?e:[e]).forEach(n=>n(st))}var Oe={},Cr=!1;function Sl(e,t){if(Cr||(Oe=je(Oe),Cr=!0),t===void 0)return Oe[e];Oe[e]=t,Ei(Oe[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&Oe[e].init()}function _l(){return Oe}var rs={};function El(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?is(e,n()):(rs[e]=n,()=>{})}function Tl(e){return Object.entries(rs).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function is(e,t,n){let r=[];for(;r.length;)r.pop()();let i=Object.entries(t).map(([o,a])=>({name:o,value:a})),s=Mi(i);return i=i.map(o=>s.find(a=>a.name===o.name)?{name:`x-bind:${o.name}`,value:`"${o.value}"`}:o),Wn(e,i,n).map(o=>{r.push(o.runCleanups),o()}),()=>{for(;r.length;)r.pop()()}}var ss={};function Cl(e,t){ss[e]=t}function Ol(e,t){return Object.entries(ss).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...i)=>r.bind(t)(...i)},enumerable:!1})}),e}var Al={get reactive(){return je},get release(){return He},get effect(){return Ne},get raw(){return hi},version:"3.14.9",flushAndStopDeferringMutations:Ma,dontAutoEvaluateFunctions:Oi,disableEffectScheduling:_a,startObservingMutations:qn,stopObservingMutations:Si,setReactivityEngine:Ea,onAttributeRemoved:bi,onAttributesAdded:wi,closestDataStack:Be,skipDuringClone:Ce,onlyDuringClone:sl,addRootSelector:Hi,addInitSelector:qi,interceptClone:jt,addScopeToNode:rt,deferMutations:Pa,mapAttributes:Kn,evaluateLater:X,interceptInit:Ka,setEvaluator:Na,mergeProxies:it,extractProp:bl,findClosest:Ve,onElRemoved:$n,closestRoot:Ft,destroyTree:Ge,interceptor:Ti,transition:Cn,setStyles:$t,mutateDom:j,directive:V,entangle:ns,throttle:ts,debounce:es,evaluate:Le,initTree:ye,nextTick:Jn,prefixed:qe,prefix:$a,plugin:xl,magic:le,store:Sl,start:Ua,clone:al,cloneNode:ol,bound:wl,$data:_i,watch:mi,walk:De,data:Cl,bind:El},st=Al;function Pl(e,t){const n=Object.create(null),r=e.split(",");for(let i=0;i<r.length;i++)n[r[i]]=!0;return t?i=>!!n[i.toLowerCase()]:i=>!!n[i]}var Ml=Object.freeze({}),Ll=Object.prototype.hasOwnProperty,Ht=(e,t)=>Ll.call(e,t),Ie=Array.isArray,Qe=e=>os(e)==="[object Map]",Il=e=>typeof e=="string",er=e=>typeof e=="symbol",qt=e=>e!==null&&typeof e=="object",kl=Object.prototype.toString,os=e=>kl.call(e),as=e=>os(e).slice(8,-1),tr=e=>Il(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Rl=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Dl=Rl(e=>e.charAt(0).toUpperCase()+e.slice(1)),ls=(e,t)=>e!==t&&(e===e||t===t),An=new WeakMap,Xe=[],he,ke=Symbol("iterate"),Pn=Symbol("Map key iterate");function Nl(e){return e&&e._isEffect===!0}function Bl(e,t=Ml){Nl(e)&&(e=e.raw);const n=$l(e,t);return t.lazy||n(),n}function zl(e){e.active&&(cs(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var Fl=0;function $l(e,t){const n=function(){if(!n.active)return e();if(!Xe.includes(n)){cs(n);try{return Hl(),Xe.push(n),he=n,e()}finally{Xe.pop(),us(),he=Xe[Xe.length-1]}}};return n.id=Fl++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function cs(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var ze=!0,nr=[];function jl(){nr.push(ze),ze=!1}function Hl(){nr.push(ze),ze=!0}function us(){const e=nr.pop();ze=e===void 0?!0:e}function oe(e,t,n){if(!ze||he===void 0)return;let r=An.get(e);r||An.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(he)||(i.add(he),he.deps.push(i),he.options.onTrack&&he.options.onTrack({effect:he,target:e,type:t,key:n}))}function Te(e,t,n,r,i,s){const o=An.get(e);if(!o)return;const a=new Set,c=u=>{u&&u.forEach(d=>{(d!==he||d.allowRecurse)&&a.add(d)})};if(t==="clear")o.forEach(c);else if(n==="length"&&Ie(e))o.forEach((u,d)=>{(d==="length"||d>=r)&&c(u)});else switch(n!==void 0&&c(o.get(n)),t){case"add":Ie(e)?tr(n)&&c(o.get("length")):(c(o.get(ke)),Qe(e)&&c(o.get(Pn)));break;case"delete":Ie(e)||(c(o.get(ke)),Qe(e)&&c(o.get(Pn)));break;case"set":Qe(e)&&c(o.get(ke));break}const l=u=>{u.options.onTrigger&&u.options.onTrigger({effect:u,target:e,key:n,type:t,newValue:r,oldValue:i,oldTarget:s}),u.options.scheduler?u.options.scheduler(u):u()};a.forEach(l)}var ql=Pl("__proto__,__v_isRef,__isVue"),ds=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(er)),Vl=fs(),Gl=fs(!0),Or=Ul();function Ul(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=F(this);for(let s=0,o=this.length;s<o;s++)oe(r,"get",s+"");const i=r[t](...n);return i===-1||i===!1?r[t](...n.map(F)):i}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){jl();const r=F(this)[t].apply(this,n);return us(),r}}),e}function fs(e=!1,t=!1){return function(r,i,s){if(i==="__v_isReactive")return!e;if(i==="__v_isReadonly")return e;if(i==="__v_raw"&&s===(e?t?lc:gs:t?ac:ms).get(r))return r;const o=Ie(r);if(!e&&o&&Ht(Or,i))return Reflect.get(Or,i,s);const a=Reflect.get(r,i,s);return(er(i)?ds.has(i):ql(i))||(e||oe(r,"get",i),t)?a:Mn(a)?!o||!tr(i)?a.value:a:qt(a)?e?vs(a):or(a):a}}var Wl=Kl();function Kl(e=!1){return function(n,r,i,s){let o=n[r];if(!e&&(i=F(i),o=F(o),!Ie(n)&&Mn(o)&&!Mn(i)))return o.value=i,!0;const a=Ie(n)&&tr(r)?Number(r)<n.length:Ht(n,r),c=Reflect.set(n,r,i,s);return n===F(s)&&(a?ls(i,o)&&Te(n,"set",r,i,o):Te(n,"add",r,i)),c}}function Xl(e,t){const n=Ht(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&Te(e,"delete",t,void 0,r),i}function Yl(e,t){const n=Reflect.has(e,t);return(!er(t)||!ds.has(t))&&oe(e,"has",t),n}function Jl(e){return oe(e,"iterate",Ie(e)?"length":ke),Reflect.ownKeys(e)}var Zl={get:Vl,set:Wl,deleteProperty:Xl,has:Yl,ownKeys:Jl},Ql={get:Gl,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},rr=e=>qt(e)?or(e):e,ir=e=>qt(e)?vs(e):e,sr=e=>e,Vt=e=>Reflect.getPrototypeOf(e);function ot(e,t,n=!1,r=!1){e=e.__v_raw;const i=F(e),s=F(t);t!==s&&!n&&oe(i,"get",t),!n&&oe(i,"get",s);const{has:o}=Vt(i),a=r?sr:n?ir:rr;if(o.call(i,t))return a(e.get(t));if(o.call(i,s))return a(e.get(s));e!==i&&e.get(t)}function at(e,t=!1){const n=this.__v_raw,r=F(n),i=F(e);return e!==i&&!t&&oe(r,"has",e),!t&&oe(r,"has",i),e===i?n.has(e):n.has(e)||n.has(i)}function lt(e,t=!1){return e=e.__v_raw,!t&&oe(F(e),"iterate",ke),Reflect.get(e,"size",e)}function Ar(e){e=F(e);const t=F(this);return Vt(t).has.call(t,e)||(t.add(e),Te(t,"add",e,e)),this}function Pr(e,t){t=F(t);const n=F(this),{has:r,get:i}=Vt(n);let s=r.call(n,e);s?hs(n,r,e):(e=F(e),s=r.call(n,e));const o=i.call(n,e);return n.set(e,t),s?ls(t,o)&&Te(n,"set",e,t,o):Te(n,"add",e,t),this}function Mr(e){const t=F(this),{has:n,get:r}=Vt(t);let i=n.call(t,e);i?hs(t,n,e):(e=F(e),i=n.call(t,e));const s=r?r.call(t,e):void 0,o=t.delete(e);return i&&Te(t,"delete",e,void 0,s),o}function Lr(){const e=F(this),t=e.size!==0,n=Qe(e)?new Map(e):new Set(e),r=e.clear();return t&&Te(e,"clear",void 0,void 0,n),r}function ct(e,t){return function(r,i){const s=this,o=s.__v_raw,a=F(o),c=t?sr:e?ir:rr;return!e&&oe(a,"iterate",ke),o.forEach((l,u)=>r.call(i,c(l),c(u),s))}}function ut(e,t,n){return function(...r){const i=this.__v_raw,s=F(i),o=Qe(s),a=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,l=i[e](...r),u=n?sr:t?ir:rr;return!t&&oe(s,"iterate",c?Pn:ke),{next(){const{value:d,done:p}=l.next();return p?{value:d,done:p}:{value:a?[u(d[0]),u(d[1])]:u(d),done:p}},[Symbol.iterator](){return this}}}}function Se(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${Dl(e)} operation ${n}failed: target is readonly.`,F(this))}return e==="delete"?!1:this}}function ec(){const e={get(s){return ot(this,s)},get size(){return lt(this)},has:at,add:Ar,set:Pr,delete:Mr,clear:Lr,forEach:ct(!1,!1)},t={get(s){return ot(this,s,!1,!0)},get size(){return lt(this)},has:at,add:Ar,set:Pr,delete:Mr,clear:Lr,forEach:ct(!1,!0)},n={get(s){return ot(this,s,!0)},get size(){return lt(this,!0)},has(s){return at.call(this,s,!0)},add:Se("add"),set:Se("set"),delete:Se("delete"),clear:Se("clear"),forEach:ct(!0,!1)},r={get(s){return ot(this,s,!0,!0)},get size(){return lt(this,!0)},has(s){return at.call(this,s,!0)},add:Se("add"),set:Se("set"),delete:Se("delete"),clear:Se("clear"),forEach:ct(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=ut(s,!1,!1),n[s]=ut(s,!0,!1),t[s]=ut(s,!1,!0),r[s]=ut(s,!0,!0)}),[e,n,t,r]}var[tc,nc,rc,ic]=ec();function ps(e,t){const n=t?e?ic:rc:e?nc:tc;return(r,i,s)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?r:Reflect.get(Ht(n,i)&&i in r?n:r,i,s)}var sc={get:ps(!1,!1)},oc={get:ps(!0,!1)};function hs(e,t,n){const r=F(n);if(r!==n&&t.call(e,r)){const i=as(e);console.warn(`Reactive ${i} contains both the raw and reactive versions of the same object${i==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var ms=new WeakMap,ac=new WeakMap,gs=new WeakMap,lc=new WeakMap;function cc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function uc(e){return e.__v_skip||!Object.isExtensible(e)?0:cc(as(e))}function or(e){return e&&e.__v_isReadonly?e:ys(e,!1,Zl,sc,ms)}function vs(e){return ys(e,!0,Ql,oc,gs)}function ys(e,t,n,r,i){if(!qt(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=i.get(e);if(s)return s;const o=uc(e);if(o===0)return e;const a=new Proxy(e,o===2?r:n);return i.set(e,a),a}function F(e){return e&&F(e.__v_raw)||e}function Mn(e){return!!(e&&e.__v_isRef===!0)}le("nextTick",()=>Jn);le("dispatch",e=>Ze.bind(Ze,e));le("watch",(e,{evaluateLater:t,cleanup:n})=>(r,i)=>{let s=t(r),a=mi(()=>{let c;return s(l=>c=l),c},i);n(a)});le("store",_l);le("data",e=>_i(e));le("root",e=>Ft(e));le("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=it(dc(e))),e._x_refs_proxy));function dc(e){let t=[];return Ve(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var Zt={};function ws(e){return Zt[e]||(Zt[e]=0),++Zt[e]}function fc(e,t){return Ve(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function pc(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=ws(t))}le("id",(e,{cleanup:t})=>(n,r=null)=>{let i=`${n}${r?`-${r}`:""}`;return hc(e,i,t,()=>{let s=fc(e,n),o=s?s._x_ids[n]:ws(n);return r?`${n}-${o}-${r}`:`${n}-${o}`})});jt((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function hc(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let i=r();return e._x_id[t]=i,n(()=>{delete e._x_id[t]}),i}le("el",e=>e);bs("Focus","focus","focus");bs("Persist","persist","persist");function bs(e,t,n){le(t,r=>re(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}V("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:i})=>{let s=r(t),o=()=>{let u;return s(d=>u=d),u},a=r(`${t} = __placeholder`),c=u=>a(()=>{},{scope:{__placeholder:u}}),l=o();c(l),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let u=e._x_model.get,d=e._x_model.set,p=ns({get(){return u()},set(g){d(g)}},{get(){return o()},set(g){c(g)}});i(p)})});V("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&re("x-teleport can only be used on a <template> tag",e);let i=Ir(n),s=e.content.cloneNode(!0).firstElementChild;e._x_teleport=s,s._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{s.addEventListener(a,c=>{c.stopPropagation(),e.dispatchEvent(new c.constructor(c.type,c))})}),rt(s,{},e);let o=(a,c,l)=>{l.includes("prepend")?c.parentNode.insertBefore(a,c):l.includes("append")?c.parentNode.insertBefore(a,c.nextSibling):c.appendChild(a)};j(()=>{o(s,i,t),Ce(()=>{ye(s)})()}),e._x_teleportPutBack=()=>{let a=Ir(n);j(()=>{o(e._x_teleport,a,t)})},r(()=>j(()=>{s.remove(),Ge(s)}))});var mc=document.createElement("div");function Ir(e){let t=Ce(()=>document.querySelector(e),()=>mc)();return t||re(`Cannot find x-teleport element for selector: "${e}"`),t}var xs=()=>{};xs.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};V("ignore",xs);V("effect",Ce((e,{expression:t},{effect:n})=>{n(X(e,t))}));function Ln(e,t,n,r){let i=e,s=c=>r(c),o={},a=(c,l)=>u=>l(c,u);if(n.includes("dot")&&(t=gc(t)),n.includes("camel")&&(t=vc(t)),n.includes("passive")&&(o.passive=!0),n.includes("capture")&&(o.capture=!0),n.includes("window")&&(i=window),n.includes("document")&&(i=document),n.includes("debounce")){let c=n[n.indexOf("debounce")+1]||"invalid-wait",l=Ct(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=es(s,l)}if(n.includes("throttle")){let c=n[n.indexOf("throttle")+1]||"invalid-wait",l=Ct(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=ts(s,l)}return n.includes("prevent")&&(s=a(s,(c,l)=>{l.preventDefault(),c(l)})),n.includes("stop")&&(s=a(s,(c,l)=>{l.stopPropagation(),c(l)})),n.includes("once")&&(s=a(s,(c,l)=>{c(l),i.removeEventListener(t,s,o)})),(n.includes("away")||n.includes("outside"))&&(i=document,s=a(s,(c,l)=>{e.contains(l.target)||l.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&c(l))})),n.includes("self")&&(s=a(s,(c,l)=>{l.target===e&&c(l)})),(wc(t)||Ss(t))&&(s=a(s,(c,l)=>{bc(l,n)||c(l)})),i.addEventListener(t,s,o),()=>{i.removeEventListener(t,s,o)}}function gc(e){return e.replace(/-/g,".")}function vc(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function Ct(e){return!Array.isArray(e)&&!isNaN(e)}function yc(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function wc(e){return["keydown","keyup"].includes(e)}function Ss(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function bc(e,t){let n=t.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(n.includes("debounce")){let s=n.indexOf("debounce");n.splice(s,Ct((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let s=n.indexOf("throttle");n.splice(s,Ct((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&kr(e.key).includes(n[0]))return!1;const i=["ctrl","shift","alt","meta","cmd","super"].filter(s=>n.includes(s));return n=n.filter(s=>!i.includes(s)),!(i.length>0&&i.filter(o=>((o==="cmd"||o==="super")&&(o="meta"),e[`${o}Key`])).length===i.length&&(Ss(e.type)||kr(e.key).includes(n[0])))}function kr(e){if(!e)return[];e=yc(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}V("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:i})=>{let s=e;t.includes("parent")&&(s=e.parentNode);let o=X(s,n),a;typeof n=="string"?a=X(s,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?a=X(s,`${n()} = __placeholder`):a=()=>{};let c=()=>{let p;return o(g=>p=g),Rr(p)?p.get():p},l=p=>{let g;o(f=>g=f),Rr(g)?g.set(p):a(()=>{},{scope:{__placeholder:p}})};typeof n=="string"&&e.type==="radio"&&j(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var u=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let d=Ee?()=>{}:Ln(e,u,t,p=>{l(Qt(e,t,p,c()))});if(t.includes("fill")&&([void 0,null,""].includes(c())||Qn(e)&&Array.isArray(c())||e.tagName.toLowerCase()==="select"&&e.multiple)&&l(Qt(e,t,{target:e},c())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=d,i(()=>e._x_removeModelListeners.default()),e.form){let p=Ln(e.form,"reset",[],g=>{Jn(()=>e._x_model&&e._x_model.set(Qt(e,t,{target:e},c())))});i(()=>p())}e._x_model={get(){return c()},set(p){l(p)}},e._x_forceModelUpdate=p=>{p===void 0&&typeof n=="string"&&n.match(/\./)&&(p=""),window.fromModel=!0,j(()=>Xi(e,"value",p)),delete window.fromModel},r(()=>{let p=c();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(p)})});function Qt(e,t,n,r){return j(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(Qn(e))if(Array.isArray(r)){let i=null;return t.includes("number")?i=en(n.target.value):t.includes("boolean")?i=yt(n.target.value):i=n.target.value,n.target.checked?r.includes(i)?r:r.concat([i]):r.filter(s=>!xc(s,i))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return en(s)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(i=>{let s=i.value||i.text;return yt(s)}):Array.from(n.target.selectedOptions).map(i=>i.value||i.text);{let i;return Qi(e)?n.target.checked?i=n.target.value:i=r:i=n.target.value,t.includes("number")?en(i):t.includes("boolean")?yt(i):t.includes("trim")?i.trim():i}}})}function en(e){let t=e?parseFloat(e):null;return Sc(t)?t:e}function xc(e,t){return e==t}function Sc(e){return!Array.isArray(e)&&!isNaN(e)}function Rr(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}V("cloak",e=>queueMicrotask(()=>j(()=>e.removeAttribute(qe("cloak")))));qi(()=>`[${qe("init")}]`);V("init",Ce((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));V("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{j(()=>{e.textContent=s})})})});V("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let i=r(t);n(()=>{i(s=>{j(()=>{e.innerHTML=s,e._x_ignoreSelf=!0,ye(e),delete e._x_ignoreSelf})})})});Kn(ki(":",Ri(qe("bind:"))));var _s=(e,{value:t,modifiers:n,expression:r,original:i},{effect:s,cleanup:o})=>{if(!t){let c={};Tl(c),X(e,r)(u=>{is(e,u,i)},{scope:c});return}if(t==="key")return _c(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=X(e,r);s(()=>a(c=>{c===void 0&&typeof r=="string"&&r.match(/\./)&&(c=""),j(()=>Xi(e,t,c,n))})),o(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};_s.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};V("bind",_s);function _c(e,t){e._x_keyExpression=t}Hi(()=>`[${qe("data")}]`);V("data",(e,{expression:t},{cleanup:n})=>{if(Ec(e))return;t=t===""?"{}":t;let r={};bn(r,e);let i={};Ol(i,r);let s=Le(e,t,{scope:i});(s===void 0||s===!0)&&(s={}),bn(s,e);let o=je(s);Ei(o);let a=rt(e,o);o.init&&Le(e,o.init),n(()=>{o.destroy&&Le(e,o.destroy),a()})});jt((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function Ec(e){return Ee?On?!0:e.hasAttribute("data-has-alpine-state"):!1}V("show",(e,{modifiers:t,expression:n},{effect:r})=>{let i=X(e,n);e._x_doHide||(e._x_doHide=()=>{j(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{j(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let s=()=>{e._x_doHide(),e._x_isShown=!1},o=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(o),c=Tn(d=>d?o():s(),d=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,d,o,s):d?a():s()}),l,u=!0;r(()=>i(d=>{!u&&d===l||(t.includes("immediate")&&(d?a():s()),c(d),l=d,u=!1)}))});V("for",(e,{expression:t},{effect:n,cleanup:r})=>{let i=Cc(t),s=X(e,i.items),o=X(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>Tc(e,i,s,o)),r(()=>{Object.values(e._x_lookup).forEach(a=>j(()=>{Ge(a),a.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function Tc(e,t,n,r){let i=o=>typeof o=="object"&&!Array.isArray(o),s=e;n(o=>{Oc(o)&&o>=0&&(o=Array.from(Array(o).keys(),v=>v+1)),o===void 0&&(o=[]);let a=e._x_lookup,c=e._x_prevKeys,l=[],u=[];if(i(o))o=Object.entries(o).map(([v,w])=>{let h=Dr(t,w,v,o);r(m=>{u.includes(m)&&re("Duplicate key on x-for",e),u.push(m)},{scope:{index:v,...h}}),l.push(h)});else for(let v=0;v<o.length;v++){let w=Dr(t,o[v],v,o);r(h=>{u.includes(h)&&re("Duplicate key on x-for",e),u.push(h)},{scope:{index:v,...w}}),l.push(w)}let d=[],p=[],g=[],f=[];for(let v=0;v<c.length;v++){let w=c[v];u.indexOf(w)===-1&&g.push(w)}c=c.filter(v=>!g.includes(v));let y="template";for(let v=0;v<u.length;v++){let w=u[v],h=c.indexOf(w);if(h===-1)c.splice(v,0,w),d.push([y,v]);else if(h!==v){let m=c.splice(v,1)[0],x=c.splice(h-1,1)[0];c.splice(v,0,x),c.splice(h,0,m),p.push([m,x])}else f.push(w);y=w}for(let v=0;v<g.length;v++){let w=g[v];w in a&&(j(()=>{Ge(a[w]),a[w].remove()}),delete a[w])}for(let v=0;v<p.length;v++){let[w,h]=p[v],m=a[w],x=a[h],_=document.createElement("div");j(()=>{x||re('x-for ":key" is undefined or invalid',s,h,a),x.after(_),m.after(x),x._x_currentIfEl&&x.after(x._x_currentIfEl),_.before(m),m._x_currentIfEl&&m.after(m._x_currentIfEl),_.remove()}),x._x_refreshXForScope(l[u.indexOf(h)])}for(let v=0;v<d.length;v++){let[w,h]=d[v],m=w==="template"?s:a[w];m._x_currentIfEl&&(m=m._x_currentIfEl);let x=l[h],_=u[h],T=document.importNode(s.content,!0).firstElementChild,M=je(x);rt(T,M,s),T._x_refreshXForScope=O=>{Object.entries(O).forEach(([L,C])=>{M[L]=C})},j(()=>{m.after(T),Ce(()=>ye(T))()}),typeof _=="object"&&re("x-for key cannot be an object, it must be a string or an integer",s),a[_]=T}for(let v=0;v<f.length;v++)a[f[v]]._x_refreshXForScope(l[u.indexOf(f[v])]);s._x_prevKeys=u})}function Cc(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,i=e.match(r);if(!i)return;let s={};s.items=i[2].trim();let o=i[1].replace(n,"").trim(),a=o.match(t);return a?(s.item=o.replace(t,"").trim(),s.index=a[1].trim(),a[2]&&(s.collection=a[2].trim())):s.item=o,s}function Dr(e,t,n,r){let i={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(o=>o.trim()).forEach((o,a)=>{i[o]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(o=>o.trim()).forEach(o=>{i[o]=t[o]}):i[e.item]=t,e.index&&(i[e.index]=n),e.collection&&(i[e.collection]=r),i}function Oc(e){return!Array.isArray(e)&&!isNaN(e)}function Es(){}Es.inline=(e,{expression:t},{cleanup:n})=>{let r=Ft(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};V("ref",Es);V("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&re("x-if can only be used on a <template> tag",e);let i=X(e,t),s=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return rt(a,{},e),j(()=>{e.after(a),Ce(()=>ye(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{j(()=>{Ge(a),a.remove()}),delete e._x_currentIfEl},a},o=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>i(a=>{a?s():o()})),r(()=>e._x_undoIf&&e._x_undoIf())});V("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(i=>pc(e,i))});jt((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});Kn(ki("@",Ri(qe("on:"))));V("on",Ce((e,{value:t,modifiers:n,expression:r},{cleanup:i})=>{let s=r?X(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let o=Ln(e,t,n,a=>{s(()=>{},{scope:{$event:a},params:[a]})});i(()=>o())}));Gt("Collapse","collapse","collapse");Gt("Intersect","intersect","intersect");Gt("Focus","trap","focus");Gt("Mask","mask","mask");function Gt(e,t,n){V(t,r=>re(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}st.setEvaluator(Pi);st.setReactivityEngine({reactive:or,effect:Bl,release:zl,raw:F});var Ac=st,Ut=Ac;function Pc(e){e.directive("intersect",e.skipDuringClone((t,{value:n,expression:r,modifiers:i},{evaluateLater:s,cleanup:o})=>{let a=s(r),c={rootMargin:Ic(i),threshold:Mc(i)},l=new IntersectionObserver(u=>{u.forEach(d=>{d.isIntersecting!==(n==="leave")&&(a(),i.includes("once")&&l.disconnect())})},c);l.observe(t),o(()=>{l.disconnect()})}))}function Mc(e){if(e.includes("full"))return .99;if(e.includes("half"))return .5;if(!e.includes("threshold"))return 0;let t=e[e.indexOf("threshold")+1];return t==="100"?1:t==="0"?0:+`.${t}`}function Lc(e){let t=e.match(/^(-?[0-9]+)(px|%)?$/);return t?t[1]+(t[2]||"px"):void 0}function Ic(e){const t="margin",n="0px 0px 0px 0px",r=e.indexOf(t);if(r===-1)return n;let i=[];for(let s=1;s<5;s++)i.push(Lc(e[r+s]||""));return i=i.filter(s=>s!==void 0),i.length?i.join(" ").trim():n}var kc=Pc;function Rc(e){e.directive("collapse",t),t.inline=(n,{modifiers:r})=>{r.includes("min")&&(n._x_doShow=()=>{},n._x_doHide=()=>{})};function t(n,{modifiers:r}){let i=Nr(r,"duration",250)/1e3,s=Nr(r,"min",0),o=!r.includes("min");n._x_isShown||(n.style.height=`${s}px`),!n._x_isShown&&o&&(n.hidden=!0),n._x_isShown||(n.style.overflow="hidden");let a=(l,u)=>{let d=e.setStyles(l,u);return u.height?()=>{}:d},c={transitionProperty:"height",transitionDuration:`${i}s`,transitionTimingFunction:"cubic-bezier(0.4, 0.0, 0.2, 1)"};n._x_transition={in(l=()=>{},u=()=>{}){o&&(n.hidden=!1),o&&(n.style.display=null);let d=n.getBoundingClientRect().height;n.style.height="auto";let p=n.getBoundingClientRect().height;d===p&&(d=s),e.transition(n,e.setStyles,{during:c,start:{height:d+"px"},end:{height:p+"px"}},()=>n._x_isShown=!0,()=>{Math.abs(n.getBoundingClientRect().height-p)<1&&(n.style.overflow=null)})},out(l=()=>{},u=()=>{}){let d=n.getBoundingClientRect().height;e.transition(n,a,{during:c,start:{height:d+"px"},end:{height:s+"px"}},()=>n.style.overflow="hidden",()=>{n._x_isShown=!1,n.style.height==`${s}px`&&o&&(n.style.display="none",n.hidden=!0)})}}}}function Nr(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r)return n;if(t==="duration"){let i=r.match(/([0-9]+)ms/);if(i)return i[1]}if(t==="min"){let i=r.match(/([0-9]+)px/);if(i)return i[1]}return r}var Dc=Rc;function Br(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function ar(e,t){e===void 0&&(e={}),t===void 0&&(t={});const n=["__proto__","constructor","prototype"];Object.keys(t).filter(r=>n.indexOf(r)<0).forEach(r=>{typeof e[r]>"u"?e[r]=t[r]:Br(t[r])&&Br(e[r])&&Object.keys(t[r]).length>0&&ar(e[r],t[r])})}const Ts={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function ge(){const e=typeof document<"u"?document:{};return ar(e,Ts),e}const Nc={document:Ts,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function Y(){const e=typeof window<"u"?window:{};return ar(e,Nc),e}function Bc(e){return e===void 0&&(e=""),e.trim().split(" ").filter(t=>!!t.trim())}function zc(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch{}try{delete t[n]}catch{}})}function Cs(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function Ot(){return Date.now()}function Fc(e){const t=Y();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function $c(e,t){t===void 0&&(t="x");const n=Y();let r,i,s;const o=Fc(e);return n.WebKitCSSMatrix?(i=o.transform||o.webkitTransform,i.split(",").length>6&&(i=i.split(", ").map(a=>a.replace(",",".")).join(", ")),s=new n.WebKitCSSMatrix(i==="none"?"":i)):(s=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=s.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?i=s.m41:r.length===16?i=parseFloat(r[12]):i=parseFloat(r[4])),t==="y"&&(n.WebKitCSSMatrix?i=s.m42:r.length===16?i=parseFloat(r[13]):i=parseFloat(r[5])),i||0}function dt(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function jc(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function te(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(r!=null&&!jc(r)){const i=Object.keys(Object(r)).filter(s=>t.indexOf(s)<0);for(let s=0,o=i.length;s<o;s+=1){const a=i[s],c=Object.getOwnPropertyDescriptor(r,a);c!==void 0&&c.enumerable&&(dt(e[a])&&dt(r[a])?r[a].__swiper__?e[a]=r[a]:te(e[a],r[a]):!dt(e[a])&&dt(r[a])?(e[a]={},r[a].__swiper__?e[a]=r[a]:te(e[a],r[a])):e[a]=r[a])}}}return e}function ft(e,t,n){e.style.setProperty(t,n)}function Os(e){let{swiper:t,targetPosition:n,side:r}=e;const i=Y(),s=-t.translate;let o=null,a;const c=t.params.speed;t.wrapperEl.style.scrollSnapType="none",i.cancelAnimationFrame(t.cssModeFrameID);const l=n>s?"next":"prev",u=(p,g)=>l==="next"&&p>=g||l==="prev"&&p<=g,d=()=>{a=new Date().getTime(),o===null&&(o=a);const p=Math.max(Math.min((a-o)/c,1),0),g=.5-Math.cos(p*Math.PI)/2;let f=s+g*(n-s);if(u(f,n)&&(f=n),t.wrapperEl.scrollTo({[r]:f}),u(f,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:f})}),i.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=i.requestAnimationFrame(d)};d()}function As(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function me(e,t){t===void 0&&(t="");const n=Y(),r=[...e.children];return n.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push(...e.assignedElements()),t?r.filter(i=>i.matches(t)):r}function Hc(e,t){const n=[t];for(;n.length>0;){const r=n.shift();if(e===r)return!0;n.push(...r.children,...r.shadowRoot?r.shadowRoot.children:[],...r.assignedElements?r.assignedElements():[])}}function qc(e,t){const n=Y();let r=t.contains(e);return!r&&n.HTMLSlotElement&&t instanceof HTMLSlotElement&&(r=[...t.assignedElements()].includes(e),r||(r=Hc(e,t))),r}function At(e){try{console.warn(e);return}catch{}}function Pt(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:Bc(t)),n}function Vc(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function Gc(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function _e(e,t){return Y().getComputedStyle(e,null).getPropertyValue(t)}function Mt(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function Ps(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function Uc(e,t){function n(r){r.target===e&&(t.call(e,r),e.removeEventListener("transitionend",n))}t&&e.addEventListener("transitionend",n)}function In(e,t,n){const r=Y();return n?e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom")):e.offsetWidth}function U(e){return(Array.isArray(e)?e:[e]).filter(t=>!!t)}function zr(e,t){t===void 0&&(t=""),typeof trustedTypes<"u"?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:n=>n}).createHTML(t):e.innerHTML=t}let tn;function Wc(){const e=Y(),t=ge();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function Ms(){return tn||(tn=Wc()),tn}let nn;function Kc(e){let{userAgent:t}=e===void 0?{}:e;const n=Ms(),r=Y(),i=r.navigator.platform,s=t||r.navigator.userAgent,o={ios:!1,android:!1},a=r.screen.width,c=r.screen.height,l=s.match(/(Android);?[\s\/]+([\d.]+)?/);let u=s.match(/(iPad).*OS\s([\d_]+)/);const d=s.match(/(iPod)(.*OS\s([\d_]+))?/),p=!u&&s.match(/(iPhone\sOS|iOS)\s([\d_]+)/),g=i==="Win32";let f=i==="MacIntel";const y=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&f&&n.touch&&y.indexOf(`${a}x${c}`)>=0&&(u=s.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),f=!1),l&&!g&&(o.os="android",o.android=!0),(u||p||d)&&(o.os="ios",o.ios=!0),o}function Ls(e){return e===void 0&&(e={}),nn||(nn=Kc(e)),nn}let rn;function Xc(){const e=Y(),t=Ls();let n=!1;function r(){const a=e.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(r()){const a=String(e.navigator.userAgent);if(a.includes("Version/")){const[c,l]=a.split("Version/")[1].split(" ")[0].split(".").map(u=>Number(u));n=c<16||c===16&&l<2}}const i=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),s=r(),o=s||i&&t.ios;return{isSafari:n||s,needPerspectiveFix:n,need3dFix:o,isWebView:i}}function Is(){return rn||(rn=Xc()),rn}function Yc(e){let{swiper:t,on:n,emit:r}=e;const i=Y();let s=null,o=null;const a=()=>{!t||t.destroyed||!t.initialized||(r("beforeResize"),r("resize"))},c=()=>{!t||t.destroyed||!t.initialized||(s=new ResizeObserver(d=>{o=i.requestAnimationFrame(()=>{const{width:p,height:g}=t;let f=p,y=g;d.forEach(v=>{let{contentBoxSize:w,contentRect:h,target:m}=v;m&&m!==t.el||(f=h?h.width:(w[0]||w).inlineSize,y=h?h.height:(w[0]||w).blockSize)}),(f!==p||y!==g)&&a()})}),s.observe(t.el))},l=()=>{o&&i.cancelAnimationFrame(o),s&&s.unobserve&&t.el&&(s.unobserve(t.el),s=null)},u=()=>{!t||t.destroyed||!t.initialized||r("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof i.ResizeObserver<"u"){c();return}i.addEventListener("resize",a),i.addEventListener("orientationchange",u)}),n("destroy",()=>{l(),i.removeEventListener("resize",a),i.removeEventListener("orientationchange",u)})}function Jc(e){let{swiper:t,extendParams:n,on:r,emit:i}=e;const s=[],o=Y(),a=function(u,d){d===void 0&&(d={});const p=o.MutationObserver||o.WebkitMutationObserver,g=new p(f=>{if(t.__preventObserver__)return;if(f.length===1){i("observerUpdate",f[0]);return}const y=function(){i("observerUpdate",f[0])};o.requestAnimationFrame?o.requestAnimationFrame(y):o.setTimeout(y,0)});g.observe(u,{attributes:typeof d.attributes>"u"?!0:d.attributes,childList:t.isElement||(typeof d.childList>"u"?!0:d).childList,characterData:typeof d.characterData>"u"?!0:d.characterData}),s.push(g)},c=()=>{if(t.params.observer){if(t.params.observeParents){const u=Ps(t.hostEl);for(let d=0;d<u.length;d+=1)a(u[d])}a(t.hostEl,{childList:t.params.observeSlideChildren}),a(t.wrapperEl,{attributes:!1})}},l=()=>{s.forEach(u=>{u.disconnect()}),s.splice(0,s.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",c),r("destroy",l)}var Zc={on(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;const i=n?"unshift":"push";return e.split(" ").forEach(s=>{r.eventsListeners[s]||(r.eventsListeners[s]=[]),r.eventsListeners[s][i](t)}),r},once(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;function i(){r.off(e,i),i.__emitterProxy&&delete i.__emitterProxy;for(var s=arguments.length,o=new Array(s),a=0;a<s;a++)o[a]=arguments[a];t.apply(r,o)}return i.__emitterProxy=t,r.on(e,i,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const r=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[r](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(r=>{typeof t>"u"?n.eventsListeners[r]=[]:n.eventsListeners[r]&&n.eventsListeners[r].forEach((i,s)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&n.eventsListeners[r].splice(s,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,r;for(var i=arguments.length,s=new Array(i),o=0;o<i;o++)s[o]=arguments[o];return typeof s[0]=="string"||Array.isArray(s[0])?(t=s[0],n=s.slice(1,s.length),r=e):(t=s[0].events,n=s[0].data,r=s[0].context||e),n.unshift(r),(Array.isArray(t)?t:t.split(" ")).forEach(c=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(l=>{l.apply(r,[c,...n])}),e.eventsListeners&&e.eventsListeners[c]&&e.eventsListeners[c].forEach(l=>{l.apply(r,n)})}),e}};function Qc(){const e=this;let t,n;const r=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=r.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=r.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(_e(r,"padding-left")||0,10)-parseInt(_e(r,"padding-right")||0,10),n=n-parseInt(_e(r,"padding-top")||0,10)-parseInt(_e(r,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function eu(){const e=this;function t(C,S){return parseFloat(C.getPropertyValue(e.getDirectionLabel(S))||0)}const n=e.params,{wrapperEl:r,slidesEl:i,size:s,rtlTranslate:o,wrongRTL:a}=e,c=e.virtual&&n.virtual.enabled,l=c?e.virtual.slides.length:e.slides.length,u=me(i,`.${e.params.slideClass}, swiper-slide`),d=c?e.virtual.slides.length:u.length;let p=[];const g=[],f=[];let y=n.slidesOffsetBefore;typeof y=="function"&&(y=n.slidesOffsetBefore.call(e));let v=n.slidesOffsetAfter;typeof v=="function"&&(v=n.slidesOffsetAfter.call(e));const w=e.snapGrid.length,h=e.slidesGrid.length;let m=n.spaceBetween,x=-y,_=0,T=0;if(typeof s>"u")return;typeof m=="string"&&m.indexOf("%")>=0?m=parseFloat(m.replace("%",""))/100*s:typeof m=="string"&&(m=parseFloat(m)),e.virtualSize=-m,u.forEach(C=>{o?C.style.marginLeft="":C.style.marginRight="",C.style.marginBottom="",C.style.marginTop=""}),n.centeredSlides&&n.cssMode&&(ft(r,"--swiper-centered-offset-before",""),ft(r,"--swiper-centered-offset-after",""));const M=n.grid&&n.grid.rows>1&&e.grid;M?e.grid.initSlides(u):e.grid&&e.grid.unsetSlides();let O;const L=n.slidesPerView==="auto"&&n.breakpoints&&Object.keys(n.breakpoints).filter(C=>typeof n.breakpoints[C].slidesPerView<"u").length>0;for(let C=0;C<d;C+=1){O=0;let S;if(u[C]&&(S=u[C]),M&&e.grid.updateSlide(C,S,u),!(u[C]&&_e(S,"display")==="none")){if(n.slidesPerView==="auto"){L&&(u[C].style[e.getDirectionLabel("width")]="");const E=getComputedStyle(S),P=S.style.transform,A=S.style.webkitTransform;if(P&&(S.style.transform="none"),A&&(S.style.webkitTransform="none"),n.roundLengths)O=e.isHorizontal()?In(S,"width",!0):In(S,"height",!0);else{const R=t(E,"width"),I=t(E,"padding-left"),B=t(E,"padding-right"),k=t(E,"margin-left"),z=t(E,"margin-right"),H=E.getPropertyValue("box-sizing");if(H&&H==="border-box")O=R+k+z;else{const{clientWidth:ie,offsetWidth:ce}=S;O=R+I+B+k+z+(ce-ie)}}P&&(S.style.transform=P),A&&(S.style.webkitTransform=A),n.roundLengths&&(O=Math.floor(O))}else O=(s-(n.slidesPerView-1)*m)/n.slidesPerView,n.roundLengths&&(O=Math.floor(O)),u[C]&&(u[C].style[e.getDirectionLabel("width")]=`${O}px`);u[C]&&(u[C].swiperSlideSize=O),f.push(O),n.centeredSlides?(x=x+O/2+_/2+m,_===0&&C!==0&&(x=x-s/2-m),C===0&&(x=x-s/2-m),Math.abs(x)<1/1e3&&(x=0),n.roundLengths&&(x=Math.floor(x)),T%n.slidesPerGroup===0&&p.push(x),g.push(x)):(n.roundLengths&&(x=Math.floor(x)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup===0&&p.push(x),g.push(x),x=x+O+m),e.virtualSize+=O+m,_=O,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,s)+v,o&&a&&(n.effect==="slide"||n.effect==="coverflow")&&(r.style.width=`${e.virtualSize+m}px`),n.setWrapperSize&&(r.style[e.getDirectionLabel("width")]=`${e.virtualSize+m}px`),M&&e.grid.updateWrapperSize(O,p),!n.centeredSlides){const C=[];for(let S=0;S<p.length;S+=1){let E=p[S];n.roundLengths&&(E=Math.floor(E)),p[S]<=e.virtualSize-s&&C.push(E)}p=C,Math.floor(e.virtualSize-s)-Math.floor(p[p.length-1])>1&&p.push(e.virtualSize-s)}if(c&&n.loop){const C=f[0]+m;if(n.slidesPerGroup>1){const S=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/n.slidesPerGroup),E=C*n.slidesPerGroup;for(let P=0;P<S;P+=1)p.push(p[p.length-1]+E)}for(let S=0;S<e.virtual.slidesBefore+e.virtual.slidesAfter;S+=1)n.slidesPerGroup===1&&p.push(p[p.length-1]+C),g.push(g[g.length-1]+C),e.virtualSize+=C}if(p.length===0&&(p=[0]),m!==0){const C=e.isHorizontal()&&o?"marginLeft":e.getDirectionLabel("marginRight");u.filter((S,E)=>!n.cssMode||n.loop?!0:E!==u.length-1).forEach(S=>{S.style[C]=`${m}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let C=0;f.forEach(E=>{C+=E+(m||0)}),C-=m;const S=C>s?C-s:0;p=p.map(E=>E<=0?-y:E>S?S+v:E)}if(n.centerInsufficientSlides){let C=0;f.forEach(E=>{C+=E+(m||0)}),C-=m;const S=(n.slidesOffsetBefore||0)+(n.slidesOffsetAfter||0);if(C+S<s){const E=(s-C-S)/2;p.forEach((P,A)=>{p[A]=P-E}),g.forEach((P,A)=>{g[A]=P+E})}}if(Object.assign(e,{slides:u,snapGrid:p,slidesGrid:g,slidesSizesGrid:f}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){ft(r,"--swiper-centered-offset-before",`${-p[0]}px`),ft(r,"--swiper-centered-offset-after",`${e.size/2-f[f.length-1]/2}px`);const C=-e.snapGrid[0],S=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(E=>E+C),e.slidesGrid=e.slidesGrid.map(E=>E+S)}if(d!==l&&e.emit("slidesLengthChange"),p.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),g.length!==h&&e.emit("slidesGridLengthChange"),n.watchSlidesProgress&&e.updateSlidesOffset(),e.emit("slidesUpdated"),!c&&!n.cssMode&&(n.effect==="slide"||n.effect==="fade")){const C=`${n.containerModifierClass}backface-hidden`,S=e.el.classList.contains(C);d<=n.maxBackfaceHiddenSlides?S||e.el.classList.add(C):S&&e.el.classList.remove(C)}}function tu(e){const t=this,n=[],r=t.virtual&&t.params.virtual.enabled;let i=0,s;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const o=a=>r?t.slides[t.getSlideIndexByData(a)]:t.slides[a];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(a=>{n.push(a)});else for(s=0;s<Math.ceil(t.params.slidesPerView);s+=1){const a=t.activeIndex+s;if(a>t.slides.length&&!r)break;n.push(o(a))}else n.push(o(t.activeIndex));for(s=0;s<n.length;s+=1)if(typeof n[s]<"u"){const a=n[s].offsetHeight;i=a>i?a:i}(i||i===0)&&(t.wrapperEl.style.height=`${i}px`)}function nu(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-n-e.cssOverflowAdjustment()}const Fr=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function ru(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:r,rtlTranslate:i,snapGrid:s}=t;if(r.length===0)return;typeof r[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let o=-e;i&&(o=e),t.visibleSlidesIndexes=[],t.visibleSlides=[];let a=n.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*t.size:typeof a=="string"&&(a=parseFloat(a));for(let c=0;c<r.length;c+=1){const l=r[c];let u=l.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(u-=r[0].swiperSlideOffset);const d=(o+(n.centeredSlides?t.minTranslate():0)-u)/(l.swiperSlideSize+a),p=(o-s[0]+(n.centeredSlides?t.minTranslate():0)-u)/(l.swiperSlideSize+a),g=-(o-u),f=g+t.slidesSizesGrid[c],y=g>=0&&g<=t.size-t.slidesSizesGrid[c],v=g>=0&&g<t.size-1||f>1&&f<=t.size||g<=0&&f>=t.size;v&&(t.visibleSlides.push(l),t.visibleSlidesIndexes.push(c)),Fr(l,v,n.slideVisibleClass),Fr(l,y,n.slideFullyVisibleClass),l.progress=i?-d:d,l.originalProgress=i?-p:p}}function iu(e){const t=this;if(typeof e>"u"){const u=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*u||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:i,isBeginning:s,isEnd:o,progressLoop:a}=t;const c=s,l=o;if(r===0)i=0,s=!0,o=!0;else{i=(e-t.minTranslate())/r;const u=Math.abs(e-t.minTranslate())<1,d=Math.abs(e-t.maxTranslate())<1;s=u||i<=0,o=d||i>=1,u&&(i=0),d&&(i=1)}if(n.loop){const u=t.getSlideIndexByData(0),d=t.getSlideIndexByData(t.slides.length-1),p=t.slidesGrid[u],g=t.slidesGrid[d],f=t.slidesGrid[t.slidesGrid.length-1],y=Math.abs(e);y>=p?a=(y-p)/f:a=(y+f-g)/f,a>1&&(a-=1)}Object.assign(t,{progress:i,progressLoop:a,isBeginning:s,isEnd:o}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),s&&!c&&t.emit("reachBeginning toEdge"),o&&!l&&t.emit("reachEnd toEdge"),(c&&!s||l&&!o)&&t.emit("fromEdge"),t.emit("progress",i)}const sn=(e,t,n)=>{t&&!e.classList.contains(n)?e.classList.add(n):!t&&e.classList.contains(n)&&e.classList.remove(n)};function su(){const e=this,{slides:t,params:n,slidesEl:r,activeIndex:i}=e,s=e.virtual&&n.virtual.enabled,o=e.grid&&n.grid&&n.grid.rows>1,a=d=>me(r,`.${n.slideClass}${d}, swiper-slide${d}`)[0];let c,l,u;if(s)if(n.loop){let d=i-e.virtual.slidesBefore;d<0&&(d=e.virtual.slides.length+d),d>=e.virtual.slides.length&&(d-=e.virtual.slides.length),c=a(`[data-swiper-slide-index="${d}"]`)}else c=a(`[data-swiper-slide-index="${i}"]`);else o?(c=t.find(d=>d.column===i),u=t.find(d=>d.column===i+1),l=t.find(d=>d.column===i-1)):c=t[i];c&&(o||(u=Gc(c,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!u&&(u=t[0]),l=Vc(c,`.${n.slideClass}, swiper-slide`)[0],n.loop&&!l===0&&(l=t[t.length-1]))),t.forEach(d=>{sn(d,d===c,n.slideActiveClass),sn(d,d===u,n.slideNextClass),sn(d,d===l,n.slidePrevClass)}),e.emitSlidesClasses()}const wt=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":`.${e.params.slideClass}`,r=t.closest(n());if(r){let i=r.querySelector(`.${e.params.lazyPreloaderClass}`);!i&&e.isElement&&(r.shadowRoot?i=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{r.shadowRoot&&(i=r.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`),i&&i.remove())})),i&&i.remove()}},on=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},kn=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const r=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),i=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const o=i,a=[o-t];a.push(...Array.from({length:t}).map((c,l)=>o+r+l)),e.slides.forEach((c,l)=>{a.includes(c.column)&&on(e,l)});return}const s=i+r-1;if(e.params.rewind||e.params.loop)for(let o=i-t;o<=s+t;o+=1){const a=(o%n+n)%n;(a<i||a>s)&&on(e,a)}else for(let o=Math.max(i-t,0);o<=Math.min(s+t,n-1);o+=1)o!==i&&(o>s||o<i)&&on(e,o)};function ou(e){const{slidesGrid:t,params:n}=e,r=e.rtlTranslate?e.translate:-e.translate;let i;for(let s=0;s<t.length;s+=1)typeof t[s+1]<"u"?r>=t[s]&&r<t[s+1]-(t[s+1]-t[s])/2?i=s:r>=t[s]&&r<t[s+1]&&(i=s+1):r>=t[s]&&(i=s);return n.normalizeSlideIndex&&(i<0||typeof i>"u")&&(i=0),i}function au(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:r,params:i,activeIndex:s,realIndex:o,snapIndex:a}=t;let c=e,l;const u=g=>{let f=g-t.virtual.slidesBefore;return f<0&&(f=t.virtual.slides.length+f),f>=t.virtual.slides.length&&(f-=t.virtual.slides.length),f};if(typeof c>"u"&&(c=ou(t)),r.indexOf(n)>=0)l=r.indexOf(n);else{const g=Math.min(i.slidesPerGroupSkip,c);l=g+Math.floor((c-g)/i.slidesPerGroup)}if(l>=r.length&&(l=r.length-1),c===s&&!t.params.loop){l!==a&&(t.snapIndex=l,t.emit("snapIndexChange"));return}if(c===s&&t.params.loop&&t.virtual&&t.params.virtual.enabled){t.realIndex=u(c);return}const d=t.grid&&i.grid&&i.grid.rows>1;let p;if(t.virtual&&i.virtual.enabled&&i.loop)p=u(c);else if(d){const g=t.slides.find(y=>y.column===c);let f=parseInt(g.getAttribute("data-swiper-slide-index"),10);Number.isNaN(f)&&(f=Math.max(t.slides.indexOf(g),0)),p=Math.floor(f/i.grid.rows)}else if(t.slides[c]){const g=t.slides[c].getAttribute("data-swiper-slide-index");g?p=parseInt(g,10):p=c}else p=c;Object.assign(t,{previousSnapIndex:a,snapIndex:l,previousRealIndex:o,realIndex:p,previousIndex:s,activeIndex:c}),t.initialized&&kn(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&(o!==p&&t.emit("realIndexChange"),t.emit("slideChange"))}function lu(e,t){const n=this,r=n.params;let i=e.closest(`.${r.slideClass}, swiper-slide`);!i&&n.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(a=>{!i&&a.matches&&a.matches(`.${r.slideClass}, swiper-slide`)&&(i=a)});let s=!1,o;if(i){for(let a=0;a<n.slides.length;a+=1)if(n.slides[a]===i){s=!0,o=a;break}}if(i&&s)n.clickedSlide=i,n.virtual&&n.params.virtual.enabled?n.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):n.clickedIndex=o;else{n.clickedSlide=void 0,n.clickedIndex=void 0;return}r.slideToClickedSlide&&n.clickedIndex!==void 0&&n.clickedIndex!==n.activeIndex&&n.slideToClickedSlide()}var cu={updateSize:Qc,updateSlides:eu,updateAutoHeight:tu,updateSlidesOffset:nu,updateSlidesProgress:ru,updateProgress:iu,updateSlidesClasses:su,updateActiveIndex:au,updateClickedSlide:lu};function uu(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:r,translate:i,wrapperEl:s}=t;if(n.virtualTranslate)return r?-i:i;if(n.cssMode)return i;let o=$c(s,e);return o+=t.cssOverflowAdjustment(),r&&(o=-o),o||0}function du(e,t){const n=this,{rtlTranslate:r,params:i,wrapperEl:s,progress:o}=n;let a=0,c=0;const l=0;n.isHorizontal()?a=r?-e:e:c=e,i.roundLengths&&(a=Math.floor(a),c=Math.floor(c)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?a:c,i.cssMode?s[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-a:-c:i.virtualTranslate||(n.isHorizontal()?a-=n.cssOverflowAdjustment():c-=n.cssOverflowAdjustment(),s.style.transform=`translate3d(${a}px, ${c}px, ${l}px)`);let u;const d=n.maxTranslate()-n.minTranslate();d===0?u=0:u=(e-n.minTranslate())/d,u!==o&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function fu(){return-this.snapGrid[0]}function pu(){return-this.snapGrid[this.snapGrid.length-1]}function hu(e,t,n,r,i){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),r===void 0&&(r=!0);const s=this,{params:o,wrapperEl:a}=s;if(s.animating&&o.preventInteractionOnTransition)return!1;const c=s.minTranslate(),l=s.maxTranslate();let u;if(r&&e>c?u=c:r&&e<l?u=l:u=e,s.updateProgress(u),o.cssMode){const d=s.isHorizontal();if(t===0)a[d?"scrollLeft":"scrollTop"]=-u;else{if(!s.support.smoothScroll)return Os({swiper:s,targetPosition:-u,side:d?"left":"top"}),!0;a.scrollTo({[d?"left":"top"]:-u,behavior:"smooth"})}return!0}return t===0?(s.setTransition(0),s.setTranslate(u),n&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionEnd"))):(s.setTransition(t),s.setTranslate(u),n&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(p){!s||s.destroyed||p.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,s.animating=!1,n&&s.emit("transitionEnd"))}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd))),!0}var mu={getTranslate:uu,setTranslate:du,minTranslate:fu,maxTranslate:pu,translateTo:hu};function gu(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration=`${e}ms`,n.wrapperEl.style.transitionDelay=e===0?"0ms":""),n.emit("setTransition",e,t)}function ks(e){let{swiper:t,runCallbacks:n,direction:r,step:i}=e;const{activeIndex:s,previousIndex:o}=t;let a=r;a||(s>o?a="next":s<o?a="prev":a="reset"),t.emit(`transition${i}`),n&&a==="reset"?t.emit(`slideResetTransition${i}`):n&&s!==o&&(t.emit(`slideChangeTransition${i}`),a==="next"?t.emit(`slideNextTransition${i}`):t.emit(`slidePrevTransition${i}`))}function vu(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;r.cssMode||(r.autoHeight&&n.updateAutoHeight(),ks({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function yu(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;n.animating=!1,!r.cssMode&&(n.setTransition(0),ks({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var wu={setTransition:gu,transitionStart:vu,transitionEnd:yu};function bu(e,t,n,r,i){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const s=this;let o=e;o<0&&(o=0);const{params:a,snapGrid:c,slidesGrid:l,previousIndex:u,activeIndex:d,rtlTranslate:p,wrapperEl:g,enabled:f}=s;if(!f&&!r&&!i||s.destroyed||s.animating&&a.preventInteractionOnTransition)return!1;typeof t>"u"&&(t=s.params.speed);const y=Math.min(s.params.slidesPerGroupSkip,o);let v=y+Math.floor((o-y)/s.params.slidesPerGroup);v>=c.length&&(v=c.length-1);const w=-c[v];if(a.normalizeSlideIndex)for(let M=0;M<l.length;M+=1){const O=-Math.floor(w*100),L=Math.floor(l[M]*100),C=Math.floor(l[M+1]*100);typeof l[M+1]<"u"?O>=L&&O<C-(C-L)/2?o=M:O>=L&&O<C&&(o=M+1):O>=L&&(o=M)}if(s.initialized&&o!==d&&(!s.allowSlideNext&&(p?w>s.translate&&w>s.minTranslate():w<s.translate&&w<s.minTranslate())||!s.allowSlidePrev&&w>s.translate&&w>s.maxTranslate()&&(d||0)!==o))return!1;o!==(u||0)&&n&&s.emit("beforeSlideChangeStart"),s.updateProgress(w);let h;o>d?h="next":o<d?h="prev":h="reset";const m=s.virtual&&s.params.virtual.enabled;if(!(m&&i)&&(p&&-w===s.translate||!p&&w===s.translate))return s.updateActiveIndex(o),a.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),a.effect!=="slide"&&s.setTranslate(w),h!=="reset"&&(s.transitionStart(n,h),s.transitionEnd(n,h)),!1;if(a.cssMode){const M=s.isHorizontal(),O=p?w:-w;if(t===0)m&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),m&&!s._cssModeVirtualInitialSet&&s.params.initialSlide>0?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[M?"scrollLeft":"scrollTop"]=O})):g[M?"scrollLeft":"scrollTop"]=O,m&&requestAnimationFrame(()=>{s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1});else{if(!s.support.smoothScroll)return Os({swiper:s,targetPosition:O,side:M?"left":"top"}),!0;g.scrollTo({[M?"left":"top"]:O,behavior:"smooth"})}return!0}const T=Is().isSafari;return m&&!i&&T&&s.isElement&&s.virtual.update(!1,!1,o),s.setTransition(t),s.setTranslate(w),s.updateActiveIndex(o),s.updateSlidesClasses(),s.emit("beforeTransitionStart",t,r),s.transitionStart(n,h),t===0?s.transitionEnd(n,h):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(O){!s||s.destroyed||O.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(n,h))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd)),!0}function xu(e,t,n,r){e===void 0&&(e=0),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this;if(i.destroyed)return;typeof t>"u"&&(t=i.params.speed);const s=i.grid&&i.params.grid&&i.params.grid.rows>1;let o=e;if(i.params.loop)if(i.virtual&&i.params.virtual.enabled)o=o+i.virtual.slidesBefore;else{let a;if(s){const p=o*i.params.grid.rows;a=i.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===p).column}else a=i.getSlideIndexByData(o);const c=s?Math.ceil(i.slides.length/i.params.grid.rows):i.slides.length,{centeredSlides:l}=i.params;let u=i.params.slidesPerView;u==="auto"?u=i.slidesPerViewDynamic():(u=Math.ceil(parseFloat(i.params.slidesPerView,10)),l&&u%2===0&&(u=u+1));let d=c-a<u;if(l&&(d=d||a<Math.ceil(u/2)),r&&l&&i.params.slidesPerView!=="auto"&&!s&&(d=!1),d){const p=l?a<i.activeIndex?"prev":"next":a-i.activeIndex-1<i.params.slidesPerView?"next":"prev";i.loopFix({direction:p,slideTo:!0,activeSlideIndex:p==="next"?a+1:a-c+1,slideRealIndex:p==="next"?i.realIndex:void 0})}if(s){const p=o*i.params.grid.rows;o=i.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===p).column}else o=i.getSlideIndexByData(o)}return requestAnimationFrame(()=>{i.slideTo(o,t,n,r)}),i}function Su(e,t,n){t===void 0&&(t=!0);const r=this,{enabled:i,params:s,animating:o}=r;if(!i||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);let a=s.slidesPerGroup;s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(a=Math.max(r.slidesPerViewDynamic("current",!0),1));const c=r.activeIndex<s.slidesPerGroupSkip?1:a,l=r.virtual&&s.virtual.enabled;if(s.loop){if(o&&!l&&s.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&s.cssMode)return requestAnimationFrame(()=>{r.slideTo(r.activeIndex+c,e,t,n)}),!0}return s.rewind&&r.isEnd?r.slideTo(0,e,t,n):r.slideTo(r.activeIndex+c,e,t,n)}function _u(e,t,n){t===void 0&&(t=!0);const r=this,{params:i,snapGrid:s,slidesGrid:o,rtlTranslate:a,enabled:c,animating:l}=r;if(!c||r.destroyed)return r;typeof e>"u"&&(e=r.params.speed);const u=r.virtual&&i.virtual.enabled;if(i.loop){if(l&&!u&&i.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}const d=a?r.translate:-r.translate;function p(h){return h<0?-Math.floor(Math.abs(h)):Math.floor(h)}const g=p(d),f=s.map(h=>p(h)),y=i.freeMode&&i.freeMode.enabled;let v=s[f.indexOf(g)-1];if(typeof v>"u"&&(i.cssMode||y)){let h;s.forEach((m,x)=>{g>=m&&(h=x)}),typeof h<"u"&&(v=y?s[h]:s[h>0?h-1:h])}let w=0;if(typeof v<"u"&&(w=o.indexOf(v),w<0&&(w=r.activeIndex-1),i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(w=w-r.slidesPerViewDynamic("previous",!0)+1,w=Math.max(w,0))),i.rewind&&r.isBeginning){const h=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(h,e,t,n)}else if(i.loop&&r.activeIndex===0&&i.cssMode)return requestAnimationFrame(()=>{r.slideTo(w,e,t,n)}),!0;return r.slideTo(w,e,t,n)}function Eu(e,t,n){t===void 0&&(t=!0);const r=this;if(!r.destroyed)return typeof e>"u"&&(e=r.params.speed),r.slideTo(r.activeIndex,e,t,n)}function Tu(e,t,n,r){t===void 0&&(t=!0),r===void 0&&(r=.5);const i=this;if(i.destroyed)return;typeof e>"u"&&(e=i.params.speed);let s=i.activeIndex;const o=Math.min(i.params.slidesPerGroupSkip,s),a=o+Math.floor((s-o)/i.params.slidesPerGroup),c=i.rtlTranslate?i.translate:-i.translate;if(c>=i.snapGrid[a]){const l=i.snapGrid[a],u=i.snapGrid[a+1];c-l>(u-l)*r&&(s+=i.params.slidesPerGroup)}else{const l=i.snapGrid[a-1],u=i.snapGrid[a];c-l<=(u-l)*r&&(s-=i.params.slidesPerGroup)}return s=Math.max(s,0),s=Math.min(s,i.slidesGrid.length-1),i.slideTo(s,e,t,n)}function Cu(){const e=this;if(e.destroyed)return;const{params:t,slidesEl:n}=e,r=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let i=e.getSlideIndexWhenGrid(e.clickedIndex),s;const o=e.isElement?"swiper-slide":`.${t.slideClass}`,a=e.grid&&e.params.grid&&e.params.grid.rows>1;if(t.loop){if(e.animating)return;s=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?e.slideToLoop(s):i>(a?(e.slides.length-r)/2-(e.params.grid.rows-1):e.slides.length-r)?(e.loopFix(),i=e.getSlideIndex(me(n,`${o}[data-swiper-slide-index="${s}"]`)[0]),Cs(()=>{e.slideTo(i)})):e.slideTo(i)}else e.slideTo(i)}var Ou={slideTo:bu,slideToLoop:xu,slideNext:Su,slidePrev:_u,slideReset:Eu,slideToClosest:Tu,slideToClickedSlide:Cu};function Au(e,t){const n=this,{params:r,slidesEl:i}=n;if(!r.loop||n.virtual&&n.params.virtual.enabled)return;const s=()=>{me(i,`.${r.slideClass}, swiper-slide`).forEach((g,f)=>{g.setAttribute("data-swiper-slide-index",f)})},o=()=>{const p=me(i,`.${r.slideBlankClass}`);p.forEach(g=>{g.remove()}),p.length>0&&(n.recalcSlides(),n.updateSlides())},a=n.grid&&r.grid&&r.grid.rows>1;r.loopAddBlankSlides&&(r.slidesPerGroup>1||a)&&o();const c=r.slidesPerGroup*(a?r.grid.rows:1),l=n.slides.length%c!==0,u=a&&n.slides.length%r.grid.rows!==0,d=p=>{for(let g=0;g<p;g+=1){const f=n.isElement?Pt("swiper-slide",[r.slideBlankClass]):Pt("div",[r.slideClass,r.slideBlankClass]);n.slidesEl.append(f)}};if(l){if(r.loopAddBlankSlides){const p=c-n.slides.length%c;d(p),n.recalcSlides(),n.updateSlides()}else At("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");s()}else if(u){if(r.loopAddBlankSlides){const p=r.grid.rows-n.slides.length%r.grid.rows;d(p),n.recalcSlides(),n.updateSlides()}else At("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");s()}else s();n.loopFix({slideRealIndex:e,direction:r.centeredSlides?void 0:"next",initial:t})}function Pu(e){let{slideRealIndex:t,slideTo:n=!0,direction:r,setTranslate:i,activeSlideIndex:s,initial:o,byController:a,byMousewheel:c}=e===void 0?{}:e;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:u,allowSlidePrev:d,allowSlideNext:p,slidesEl:g,params:f}=l,{centeredSlides:y,initialSlide:v}=f;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&f.virtual.enabled){n&&(!f.centeredSlides&&l.snapIndex===0?l.slideTo(l.virtual.slides.length,0,!1,!0):f.centeredSlides&&l.snapIndex<f.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0)),l.allowSlidePrev=d,l.allowSlideNext=p,l.emit("loopFix");return}let w=f.slidesPerView;w==="auto"?w=l.slidesPerViewDynamic():(w=Math.ceil(parseFloat(f.slidesPerView,10)),y&&w%2===0&&(w=w+1));const h=f.slidesPerGroupAuto?w:f.slidesPerGroup;let m=y?Math.max(h,Math.ceil(w/2)):h;m%h!==0&&(m+=h-m%h),m+=f.loopAdditionalSlides,l.loopedSlides=m;const x=l.grid&&f.grid&&f.grid.rows>1;u.length<w+m||l.params.effect==="cards"&&u.length<w+m*2?At("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):x&&f.grid.fill==="row"&&At("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const _=[],T=[],M=x?Math.ceil(u.length/f.grid.rows):u.length,O=o&&M-v<w&&!y;let L=O?v:l.activeIndex;typeof s>"u"?s=l.getSlideIndex(u.find(I=>I.classList.contains(f.slideActiveClass))):L=s;const C=r==="next"||!r,S=r==="prev"||!r;let E=0,P=0;const R=(x?u[s].column:s)+(y&&typeof i>"u"?-w/2+.5:0);if(R<m){E=Math.max(m-R,h);for(let I=0;I<m-R;I+=1){const B=I-Math.floor(I/M)*M;if(x){const k=M-B-1;for(let z=u.length-1;z>=0;z-=1)u[z].column===k&&_.push(z)}else _.push(M-B-1)}}else if(R+w>M-m){P=Math.max(R-(M-m*2),h),O&&(P=Math.max(P,w-M+v+1));for(let I=0;I<P;I+=1){const B=I-Math.floor(I/M)*M;x?u.forEach((k,z)=>{k.column===B&&T.push(z)}):T.push(B)}}if(l.__preventObserver__=!0,requestAnimationFrame(()=>{l.__preventObserver__=!1}),l.params.effect==="cards"&&u.length<w+m*2&&(T.includes(s)&&T.splice(T.indexOf(s),1),_.includes(s)&&_.splice(_.indexOf(s),1)),S&&_.forEach(I=>{u[I].swiperLoopMoveDOM=!0,g.prepend(u[I]),u[I].swiperLoopMoveDOM=!1}),C&&T.forEach(I=>{u[I].swiperLoopMoveDOM=!0,g.append(u[I]),u[I].swiperLoopMoveDOM=!1}),l.recalcSlides(),f.slidesPerView==="auto"?l.updateSlides():x&&(_.length>0&&S||T.length>0&&C)&&l.slides.forEach((I,B)=>{l.grid.updateSlide(B,I,l.slides)}),f.watchSlidesProgress&&l.updateSlidesOffset(),n){if(_.length>0&&S){if(typeof t>"u"){const I=l.slidesGrid[L],k=l.slidesGrid[L+E]-I;c?l.setTranslate(l.translate-k):(l.slideTo(L+Math.ceil(E),0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-k,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-k))}else if(i){const I=x?_.length/f.grid.rows:_.length;l.slideTo(l.activeIndex+I,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(T.length>0&&C)if(typeof t>"u"){const I=l.slidesGrid[L],k=l.slidesGrid[L-P]-I;c?l.setTranslate(l.translate-k):(l.slideTo(L-P,0,!1,!0),i&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-k,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-k))}else{const I=x?T.length/f.grid.rows:T.length;l.slideTo(l.activeIndex-I,0,!1,!0)}}if(l.allowSlidePrev=d,l.allowSlideNext=p,l.controller&&l.controller.control&&!a){const I={slideRealIndex:t,direction:r,setTranslate:i,activeSlideIndex:s,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach(B=>{!B.destroyed&&B.params.loop&&B.loopFix({...I,slideTo:B.params.slidesPerView===f.slidesPerView?n:!1})}):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...I,slideTo:l.controller.control.params.slidesPerView===f.slidesPerView?n:!1})}l.emit("loopFix")}function Mu(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||!n||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const r=[];e.slides.forEach(i=>{const s=typeof i.swiperSlideIndex>"u"?i.getAttribute("data-swiper-slide-index")*1:i.swiperSlideIndex;r[s]=i}),e.slides.forEach(i=>{i.removeAttribute("data-swiper-slide-index")}),r.forEach(i=>{n.append(i)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var Lu={loopCreate:Au,loopFix:Pu,loopDestroy:Mu};function Iu(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function ku(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var Ru={setGrabCursor:Iu,unsetGrabCursor:ku};function Du(e,t){t===void 0&&(t=this);function n(r){if(!r||r===ge()||r===Y())return null;r.assignedSlot&&(r=r.assignedSlot);const i=r.closest(e);return!i&&!r.getRootNode?null:i||n(r.getRootNode().host)}return n(t)}function $r(e,t,n){const r=Y(),{params:i}=e,s=i.edgeSwipeDetection,o=i.edgeSwipeThreshold;return s&&(n<=o||n>=r.innerWidth-o)?s==="prevent"?(t.preventDefault(),!0):!1:!0}function Nu(e){const t=this,n=ge();let r=e;r.originalEvent&&(r=r.originalEvent);const i=t.touchEventsData;if(r.type==="pointerdown"){if(i.pointerId!==null&&i.pointerId!==r.pointerId)return;i.pointerId=r.pointerId}else r.type==="touchstart"&&r.targetTouches.length===1&&(i.touchId=r.targetTouches[0].identifier);if(r.type==="touchstart"){$r(t,r,r.targetTouches[0].pageX);return}const{params:s,touches:o,enabled:a}=t;if(!a||!s.simulateTouch&&r.pointerType==="mouse"||t.animating&&s.preventInteractionOnTransition)return;!t.animating&&s.cssMode&&s.loop&&t.loopFix();let c=r.target;if(s.touchEventsTarget==="wrapper"&&!qc(c,t.wrapperEl)||"which"in r&&r.which===3||"button"in r&&r.button>0||i.isTouched&&i.isMoved)return;const l=!!s.noSwipingClass&&s.noSwipingClass!=="",u=r.composedPath?r.composedPath():r.path;l&&r.target&&r.target.shadowRoot&&u&&(c=u[0]);const d=s.noSwipingSelector?s.noSwipingSelector:`.${s.noSwipingClass}`,p=!!(r.target&&r.target.shadowRoot);if(s.noSwiping&&(p?Du(d,c):c.closest(d))){t.allowClick=!0;return}if(s.swipeHandler&&!c.closest(s.swipeHandler))return;o.currentX=r.pageX,o.currentY=r.pageY;const g=o.currentX,f=o.currentY;if(!$r(t,r,g))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=g,o.startY=f,i.touchStartTime=Ot(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,s.threshold>0&&(i.allowThresholdMove=!1);let y=!0;c.matches(i.focusableElements)&&(y=!1,c.nodeName==="SELECT"&&(i.isTouched=!1)),n.activeElement&&n.activeElement.matches(i.focusableElements)&&n.activeElement!==c&&(r.pointerType==="mouse"||r.pointerType!=="mouse"&&!c.matches(i.focusableElements))&&n.activeElement.blur();const v=y&&t.allowTouchMove&&s.touchStartPreventDefault;(s.touchStartForcePreventDefault||v)&&!c.isContentEditable&&r.preventDefault(),s.freeMode&&s.freeMode.enabled&&t.freeMode&&t.animating&&!s.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",r)}function Bu(e){const t=ge(),n=this,r=n.touchEventsData,{params:i,touches:s,rtlTranslate:o,enabled:a}=n;if(!a||!i.simulateTouch&&e.pointerType==="mouse")return;let c=e;if(c.originalEvent&&(c=c.originalEvent),c.type==="pointermove"&&(r.touchId!==null||c.pointerId!==r.pointerId))return;let l;if(c.type==="touchmove"){if(l=[...c.changedTouches].find(T=>T.identifier===r.touchId),!l||l.identifier!==r.touchId)return}else l=c;if(!r.isTouched){r.startMoving&&r.isScrolling&&n.emit("touchMoveOpposite",c);return}const u=l.pageX,d=l.pageY;if(c.preventedByNestedSwiper){s.startX=u,s.startY=d;return}if(!n.allowTouchMove){c.target.matches(r.focusableElements)||(n.allowClick=!1),r.isTouched&&(Object.assign(s,{startX:u,startY:d,currentX:u,currentY:d}),r.touchStartTime=Ot());return}if(i.touchReleaseOnEdges&&!i.loop)if(n.isVertical()){if(d<s.startY&&n.translate<=n.maxTranslate()||d>s.startY&&n.translate>=n.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else{if(o&&(u>s.startX&&-n.translate<=n.maxTranslate()||u<s.startX&&-n.translate>=n.minTranslate()))return;if(!o&&(u<s.startX&&n.translate<=n.maxTranslate()||u>s.startX&&n.translate>=n.minTranslate()))return}if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==c.target&&c.pointerType!=="mouse"&&t.activeElement.blur(),t.activeElement&&c.target===t.activeElement&&c.target.matches(r.focusableElements)){r.isMoved=!0,n.allowClick=!1;return}r.allowTouchCallbacks&&n.emit("touchMove",c),s.previousX=s.currentX,s.previousY=s.currentY,s.currentX=u,s.currentY=d;const p=s.currentX-s.startX,g=s.currentY-s.startY;if(n.params.threshold&&Math.sqrt(p**2+g**2)<n.params.threshold)return;if(typeof r.isScrolling>"u"){let T;n.isHorizontal()&&s.currentY===s.startY||n.isVertical()&&s.currentX===s.startX?r.isScrolling=!1:p*p+g*g>=25&&(T=Math.atan2(Math.abs(g),Math.abs(p))*180/Math.PI,r.isScrolling=n.isHorizontal()?T>i.touchAngle:90-T>i.touchAngle)}if(r.isScrolling&&n.emit("touchMoveOpposite",c),typeof r.startMoving>"u"&&(s.currentX!==s.startX||s.currentY!==s.startY)&&(r.startMoving=!0),r.isScrolling||c.type==="touchmove"&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;n.allowClick=!1,!i.cssMode&&c.cancelable&&c.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&c.stopPropagation();let f=n.isHorizontal()?p:g,y=n.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY;i.oneWayMovement&&(f=Math.abs(f)*(o?1:-1),y=Math.abs(y)*(o?1:-1)),s.diff=f,f*=i.touchRatio,o&&(f=-f,y=-y);const v=n.touchesDirection;n.swipeDirection=f>0?"prev":"next",n.touchesDirection=y>0?"prev":"next";const w=n.params.loop&&!i.cssMode,h=n.touchesDirection==="next"&&n.allowSlideNext||n.touchesDirection==="prev"&&n.allowSlidePrev;if(!r.isMoved){if(w&&h&&n.loopFix({direction:n.swipeDirection}),r.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});n.wrapperEl.dispatchEvent(T)}r.allowMomentumBounce=!1,i.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",c)}let m;if(new Date().getTime(),i._loopSwapReset!==!1&&r.isMoved&&r.allowThresholdMove&&v!==n.touchesDirection&&w&&h&&Math.abs(f)>=1){Object.assign(s,{startX:u,startY:d,currentX:u,currentY:d,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}n.emit("sliderMove",c),r.isMoved=!0,r.currentTranslate=f+r.startTranslate;let x=!0,_=i.resistanceRatio;if(i.touchReleaseOnEdges&&(_=0),f>0?(w&&h&&!m&&r.allowThresholdMove&&r.currentTranslate>(i.centeredSlides?n.minTranslate()-n.slidesSizesGrid[n.activeIndex+1]-(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.activeIndex+1]+n.params.spaceBetween:0)-n.params.spaceBetween:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>n.minTranslate()&&(x=!1,i.resistance&&(r.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+r.startTranslate+f)**_))):f<0&&(w&&h&&!m&&r.allowThresholdMove&&r.currentTranslate<(i.centeredSlides?n.maxTranslate()+n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween+(i.slidesPerView!=="auto"&&n.slides.length-i.slidesPerView>=2?n.slidesSizesGrid[n.slidesSizesGrid.length-1]+n.params.spaceBetween:0):n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(i.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),r.currentTranslate<n.maxTranslate()&&(x=!1,i.resistance&&(r.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-r.startTranslate-f)**_))),x&&(c.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(r.currentTranslate=r.startTranslate),i.threshold>0)if(Math.abs(f)>i.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,r.currentTranslate=r.startTranslate,s.diff=n.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY;return}}else{r.currentTranslate=r.startTranslate;return}!i.followFinger||i.cssMode||((i.freeMode&&i.freeMode.enabled&&n.freeMode||i.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(r.currentTranslate),n.setTranslate(r.currentTranslate))}function zu(e){const t=this,n=t.touchEventsData;let r=e;r.originalEvent&&(r=r.originalEvent);let i;if(r.type==="touchend"||r.type==="touchcancel"){if(i=[...r.changedTouches].find(_=>_.identifier===n.touchId),!i||i.identifier!==n.touchId)return}else{if(n.touchId!==null||r.pointerId!==n.pointerId)return;i=r}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(r.type)&&!(["pointercancel","contextmenu"].includes(r.type)&&(t.browser.isSafari||t.browser.isWebView)))return;n.pointerId=null,n.touchId=null;const{params:o,touches:a,rtlTranslate:c,slidesGrid:l,enabled:u}=t;if(!u||!o.simulateTouch&&r.pointerType==="mouse")return;if(n.allowTouchCallbacks&&t.emit("touchEnd",r),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&o.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}o.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const d=Ot(),p=d-n.touchStartTime;if(t.allowClick){const _=r.path||r.composedPath&&r.composedPath();t.updateClickedSlide(_&&_[0]||r.target,_),t.emit("tap click",r),p<300&&d-n.lastClickTime<300&&t.emit("doubleTap doubleClick",r)}if(n.lastClickTime=Ot(),Cs(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||a.diff===0&&!n.loopSwapReset||n.currentTranslate===n.startTranslate&&!n.loopSwapReset){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let g;if(o.followFinger?g=c?t.translate:-t.translate:g=-n.currentTranslate,o.cssMode)return;if(o.freeMode&&o.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:g});return}const f=g>=-t.maxTranslate()&&!t.params.loop;let y=0,v=t.slidesSizesGrid[0];for(let _=0;_<l.length;_+=_<o.slidesPerGroupSkip?1:o.slidesPerGroup){const T=_<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;typeof l[_+T]<"u"?(f||g>=l[_]&&g<l[_+T])&&(y=_,v=l[_+T]-l[_]):(f||g>=l[_])&&(y=_,v=l[l.length-1]-l[l.length-2])}let w=null,h=null;o.rewind&&(t.isBeginning?h=o.virtual&&o.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(w=0));const m=(g-l[y])/v,x=y<o.slidesPerGroupSkip-1?1:o.slidesPerGroup;if(p>o.longSwipesMs){if(!o.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(m>=o.longSwipesRatio?t.slideTo(o.rewind&&t.isEnd?w:y+x):t.slideTo(y)),t.swipeDirection==="prev"&&(m>1-o.longSwipesRatio?t.slideTo(y+x):h!==null&&m<0&&Math.abs(m)>o.longSwipesRatio?t.slideTo(h):t.slideTo(y))}else{if(!o.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(r.target===t.navigation.nextEl||r.target===t.navigation.prevEl)?r.target===t.navigation.nextEl?t.slideTo(y+x):t.slideTo(y):(t.swipeDirection==="next"&&t.slideTo(w!==null?w:y+x),t.swipeDirection==="prev"&&t.slideTo(h!==null?h:y))}}function jr(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:i,snapGrid:s}=e,o=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const a=o&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!a?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!o?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=i,e.allowSlideNext=r,e.params.watchOverflow&&s!==e.snapGrid&&e.checkOverflow()}function Fu(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function $u(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:r}=e;if(!r)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let i;const s=e.maxTranslate()-e.minTranslate();s===0?i=0:i=(e.translate-e.minTranslate())/s,i!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function ju(e){const t=this;wt(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}function Hu(){const e=this;e.documentTouchHandlerProceeded||(e.documentTouchHandlerProceeded=!0,e.params.touchReleaseOnEdges&&(e.el.style.touchAction="auto"))}const Rs=(e,t)=>{const n=ge(),{params:r,el:i,wrapperEl:s,device:o}=e,a=!!r.nested,c=t==="on"?"addEventListener":"removeEventListener",l=t;!i||typeof i=="string"||(n[c]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:a}),i[c]("touchstart",e.onTouchStart,{passive:!1}),i[c]("pointerdown",e.onTouchStart,{passive:!1}),n[c]("touchmove",e.onTouchMove,{passive:!1,capture:a}),n[c]("pointermove",e.onTouchMove,{passive:!1,capture:a}),n[c]("touchend",e.onTouchEnd,{passive:!0}),n[c]("pointerup",e.onTouchEnd,{passive:!0}),n[c]("pointercancel",e.onTouchEnd,{passive:!0}),n[c]("touchcancel",e.onTouchEnd,{passive:!0}),n[c]("pointerout",e.onTouchEnd,{passive:!0}),n[c]("pointerleave",e.onTouchEnd,{passive:!0}),n[c]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&i[c]("click",e.onClick,!0),r.cssMode&&s[c]("scroll",e.onScroll),r.updateOnWindowResize?e[l](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",jr,!0):e[l]("observerUpdate",jr,!0),i[c]("load",e.onLoad,{capture:!0}))};function qu(){const e=this,{params:t}=e;e.onTouchStart=Nu.bind(e),e.onTouchMove=Bu.bind(e),e.onTouchEnd=zu.bind(e),e.onDocumentTouchStart=Hu.bind(e),t.cssMode&&(e.onScroll=$u.bind(e)),e.onClick=Fu.bind(e),e.onLoad=ju.bind(e),Rs(e,"on")}function Vu(){Rs(this,"off")}var Gu={attachEvents:qu,detachEvents:Vu};const Hr=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function Uu(){const e=this,{realIndex:t,initialized:n,params:r,el:i}=e,s=r.breakpoints;if(!s||s&&Object.keys(s).length===0)return;const o=ge(),a=r.breakpointsBase==="window"||!r.breakpointsBase?r.breakpointsBase:"container",c=["window","container"].includes(r.breakpointsBase)||!r.breakpointsBase?e.el:o.querySelector(r.breakpointsBase),l=e.getBreakpoint(s,a,c);if(!l||e.currentBreakpoint===l)return;const d=(l in s?s[l]:void 0)||e.originalParams,p=Hr(e,r),g=Hr(e,d),f=e.params.grabCursor,y=d.grabCursor,v=r.enabled;p&&!g?(i.classList.remove(`${r.containerModifierClass}grid`,`${r.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&g&&(i.classList.add(`${r.containerModifierClass}grid`),(d.grid.fill&&d.grid.fill==="column"||!d.grid.fill&&r.grid.fill==="column")&&i.classList.add(`${r.containerModifierClass}grid-column`),e.emitContainerClasses()),f&&!y?e.unsetGrabCursor():!f&&y&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(T=>{if(typeof d[T]>"u")return;const M=r[T]&&r[T].enabled,O=d[T]&&d[T].enabled;M&&!O&&e[T].disable(),!M&&O&&e[T].enable()});const w=d.direction&&d.direction!==r.direction,h=r.loop&&(d.slidesPerView!==r.slidesPerView||w),m=r.loop;w&&n&&e.changeDirection(),te(e.params,d);const x=e.params.enabled,_=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),v&&!x?e.disable():!v&&x&&e.enable(),e.currentBreakpoint=l,e.emit("_beforeBreakpoint",d),n&&(h?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!m&&_?(e.loopCreate(t),e.updateSlides()):m&&!_&&e.loopDestroy()),e.emit("breakpoint",d)}function Wu(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let r=!1;const i=Y(),s=t==="window"?i.innerHeight:n.clientHeight,o=Object.keys(e).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const c=parseFloat(a.substr(1));return{value:s*c,point:a}}return{value:a,point:a}});o.sort((a,c)=>parseInt(a.value,10)-parseInt(c.value,10));for(let a=0;a<o.length;a+=1){const{point:c,value:l}=o[a];t==="window"?i.matchMedia(`(min-width: ${l}px)`).matches&&(r=c):l<=n.clientWidth&&(r=c)}return r||"max"}var Ku={setBreakpoint:Uu,getBreakpoint:Wu};function Xu(e,t){const n=[];return e.forEach(r=>{typeof r=="object"?Object.keys(r).forEach(i=>{r[i]&&n.push(t+i)}):typeof r=="string"&&n.push(t+r)}),n}function Yu(){const e=this,{classNames:t,params:n,rtl:r,el:i,device:s}=e,o=Xu(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:r},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:s.android},{ios:s.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...o),i.classList.add(...t),e.emitContainerClasses()}function Ju(){const e=this,{el:t,classNames:n}=e;!t||typeof t=="string"||(t.classList.remove(...n),e.emitContainerClasses())}var Zu={addClasses:Yu,removeClasses:Ju};function Qu(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:r}=n;if(r){const i=e.slides.length-1,s=e.slidesGrid[i]+e.slidesSizesGrid[i]+r*2;e.isLocked=e.size>s}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var ed={checkOverflow:Qu},qr={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function td(e,t){return function(r){r===void 0&&(r={});const i=Object.keys(r)[0],s=r[i];if(typeof s!="object"||s===null){te(t,r);return}if(e[i]===!0&&(e[i]={enabled:!0}),i==="navigation"&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),!(i in e&&"enabled"in s)){te(t,r);return}typeof e[i]=="object"&&!("enabled"in e[i])&&(e[i].enabled=!0),e[i]||(e[i]={enabled:!1}),te(t,r)}}const an={eventsEmitter:Zc,update:cu,translate:mu,transition:wu,slide:Ou,loop:Lu,grabCursor:Ru,events:Gu,breakpoints:Ku,checkOverflow:ed,classes:Zu},ln={};class Z{constructor(){let t,n;for(var r=arguments.length,i=new Array(r),s=0;s<r;s++)i[s]=arguments[s];i.length===1&&i[0].constructor&&Object.prototype.toString.call(i[0]).slice(8,-1)==="Object"?n=i[0]:[t,n]=i,n||(n={}),n=te({},n),t&&!n.el&&(n.el=t);const o=ge();if(n.el&&typeof n.el=="string"&&o.querySelectorAll(n.el).length>1){const u=[];return o.querySelectorAll(n.el).forEach(d=>{const p=te({},n,{el:d});u.push(new Z(p))}),u}const a=this;a.__swiper__=!0,a.support=Ms(),a.device=Ls({userAgent:n.userAgent}),a.browser=Is(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],n.modules&&Array.isArray(n.modules)&&a.modules.push(...n.modules);const c={};a.modules.forEach(u=>{u({params:n,swiper:a,extendParams:td(n,c),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const l=te({},qr,c);return a.params=te({},l,ln,n),a.originalParams=te({},a.params),a.passedParams=te({},n),a.params&&a.params.on&&Object.keys(a.params.on).forEach(u=>{a.on(u,a.params.on[u])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:n,params:r}=this,i=me(n,`.${r.slideClass}, swiper-slide`),s=Mt(i[0]);return Mt(t)-s}getSlideIndexByData(t){return this.getSlideIndex(this.slides.find(n=>n.getAttribute("data-swiper-slide-index")*1===t))}getSlideIndexWhenGrid(t){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?t=Math.floor(t/this.params.grid.rows):this.params.grid.fill==="row"&&(t=t%Math.ceil(this.slides.length/this.params.grid.rows))),t}recalcSlides(){const t=this,{slidesEl:n,params:r}=t;t.slides=me(n,`.${r.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const r=this;t=Math.min(Math.max(t,0),1);const i=r.minTranslate(),o=(r.maxTranslate()-i)*t+i;r.translateTo(o,typeof n>"u"?0:n),r.updateActiveIndex(),r.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(r=>r.indexOf("swiper")===0||r.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(r=>r.indexOf("swiper-slide")===0||r.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(r=>{const i=t.getSlideClasses(r);n.push({slideEl:r,classNames:i}),t.emit("_slideClass",r,i)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const r=this,{params:i,slides:s,slidesGrid:o,slidesSizesGrid:a,size:c,activeIndex:l}=r;let u=1;if(typeof i.slidesPerView=="number")return i.slidesPerView;if(i.centeredSlides){let d=s[l]?Math.ceil(s[l].swiperSlideSize):0,p;for(let g=l+1;g<s.length;g+=1)s[g]&&!p&&(d+=Math.ceil(s[g].swiperSlideSize),u+=1,d>c&&(p=!0));for(let g=l-1;g>=0;g-=1)s[g]&&!p&&(d+=s[g].swiperSlideSize,u+=1,d>c&&(p=!0))}else if(t==="current")for(let d=l+1;d<s.length;d+=1)(n?o[d]+a[d]-o[l]<c:o[d]-o[l]<c)&&(u+=1);else for(let d=l-1;d>=0;d-=1)o[l]-o[d]<c&&(u+=1);return u}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:r}=t;r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&wt(t,o)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function i(){const o=t.rtlTranslate?t.translate*-1:t.translate,a=Math.min(Math.max(o,t.maxTranslate()),t.minTranslate());t.setTranslate(a),t.updateActiveIndex(),t.updateSlidesClasses()}let s;if(r.freeMode&&r.freeMode.enabled&&!r.cssMode)i(),r.autoHeight&&t.updateAutoHeight();else{if((r.slidesPerView==="auto"||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){const o=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;s=t.slideTo(o.length-1,0,!1,!0)}else s=t.slideTo(t.activeIndex,0,!1,!0);s||i()}r.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const r=this,i=r.params.direction;return t||(t=i==="horizontal"?"vertical":"horizontal"),t===i||t!=="horizontal"&&t!=="vertical"||(r.el.classList.remove(`${r.params.containerModifierClass}${i}`),r.el.classList.add(`${r.params.containerModifierClass}${t}`),r.emitContainerClasses(),r.params.direction=t,r.slides.forEach(s=>{t==="vertical"?s.style.width="":s.style.height=""}),r.emit("changeDirection"),n&&r.update()),r}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add(`${n.params.containerModifierClass}rtl`),n.el.dir="rtl"):(n.el.classList.remove(`${n.params.containerModifierClass}rtl`),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let r=t||n.params.el;if(typeof r=="string"&&(r=document.querySelector(r)),!r)return!1;r.swiper=n,r.parentNode&&r.parentNode.host&&r.parentNode.host.nodeName===n.params.swiperElementNodeName.toUpperCase()&&(n.isElement=!0);const i=()=>`.${(n.params.wrapperClass||"").trim().split(" ").join(".")}`;let o=(()=>r&&r.shadowRoot&&r.shadowRoot.querySelector?r.shadowRoot.querySelector(i()):me(r,i())[0])();return!o&&n.params.createElements&&(o=Pt("div",n.params.wrapperClass),r.append(o),me(r,`.${n.params.slideClass}`).forEach(a=>{o.append(a)})),Object.assign(n,{el:r,wrapperEl:o,slidesEl:n.isElement&&!r.parentNode.host.slideSlots?r.parentNode.host:o,hostEl:n.isElement?r.parentNode.host:r,mounted:!0,rtl:r.dir.toLowerCase()==="rtl"||_e(r,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(r.dir.toLowerCase()==="rtl"||_e(r,"direction")==="rtl"),wrongRTL:_e(o,"display")==="-webkit-box"}),!0}init(t){const n=this;if(n.initialized||n.mount(t)===!1)return n;n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(void 0,!0),n.attachEvents();const i=[...n.el.querySelectorAll('[loading="lazy"]')];return n.isElement&&i.push(...n.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach(s=>{s.complete?wt(n,s):s.addEventListener("load",o=>{wt(n,o.target)})}),kn(n),n.initialized=!0,kn(n),n.emit("init"),n.emit("afterInit"),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const r=this,{params:i,el:s,wrapperEl:o,slides:a}=r;return typeof r.params>"u"||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),i.loop&&r.loopDestroy(),n&&(r.removeClasses(),s&&typeof s!="string"&&s.removeAttribute("style"),o&&o.removeAttribute("style"),a&&a.length&&a.forEach(c=>{c.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),c.removeAttribute("style"),c.removeAttribute("data-swiper-slide-index")})),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(c=>{r.off(c)}),t!==!1&&(r.el&&typeof r.el!="string"&&(r.el.swiper=null),zc(r)),r.destroyed=!0),null}static extendDefaults(t){te(ln,t)}static get extendedDefaults(){return ln}static get defaults(){return qr}static installModule(t){Z.prototype.__modules__||(Z.prototype.__modules__=[]);const n=Z.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>Z.installModule(n)),Z):(Z.installModule(t),Z)}}Object.keys(an).forEach(e=>{Object.keys(an[e]).forEach(t=>{Z.prototype[t]=an[e][t]})});Z.use([Yc,Jc]);function Ds(e,t,n,r){return e.params.createElements&&Object.keys(r).forEach(i=>{if(!n[i]&&n.auto===!0){let s=me(e.el,`.${r[i]}`)[0];s||(s=Pt("div",r[i]),s.className=r[i],e.el.append(s)),n[i]=s,t[i]=s}}),n}function nd(e){let{swiper:t,extendParams:n,on:r,emit:i}=e;n({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null};function s(f){let y;return f&&typeof f=="string"&&t.isElement&&(y=t.el.querySelector(f)||t.hostEl.querySelector(f),y)?y:(f&&(typeof f=="string"&&(y=[...document.querySelectorAll(f)]),t.params.uniqueNavElements&&typeof f=="string"&&y&&y.length>1&&t.el.querySelectorAll(f).length===1?y=t.el.querySelector(f):y&&y.length===1&&(y=y[0])),f&&!y?f:y)}function o(f,y){const v=t.params.navigation;f=U(f),f.forEach(w=>{w&&(w.classList[y?"add":"remove"](...v.disabledClass.split(" ")),w.tagName==="BUTTON"&&(w.disabled=y),t.params.watchOverflow&&t.enabled&&w.classList[t.isLocked?"add":"remove"](v.lockClass))})}function a(){const{nextEl:f,prevEl:y}=t.navigation;if(t.params.loop){o(y,!1),o(f,!1);return}o(y,t.isBeginning&&!t.params.rewind),o(f,t.isEnd&&!t.params.rewind)}function c(f){f.preventDefault(),!(t.isBeginning&&!t.params.loop&&!t.params.rewind)&&(t.slidePrev(),i("navigationPrev"))}function l(f){f.preventDefault(),!(t.isEnd&&!t.params.loop&&!t.params.rewind)&&(t.slideNext(),i("navigationNext"))}function u(){const f=t.params.navigation;if(t.params.navigation=Ds(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(f.nextEl||f.prevEl))return;let y=s(f.nextEl),v=s(f.prevEl);Object.assign(t.navigation,{nextEl:y,prevEl:v}),y=U(y),v=U(v);const w=(h,m)=>{h&&h.addEventListener("click",m==="next"?l:c),!t.enabled&&h&&h.classList.add(...f.lockClass.split(" "))};y.forEach(h=>w(h,"next")),v.forEach(h=>w(h,"prev"))}function d(){let{nextEl:f,prevEl:y}=t.navigation;f=U(f),y=U(y);const v=(w,h)=>{w.removeEventListener("click",h==="next"?l:c),w.classList.remove(...t.params.navigation.disabledClass.split(" "))};f.forEach(w=>v(w,"next")),y.forEach(w=>v(w,"prev"))}r("init",()=>{t.params.navigation.enabled===!1?g():(u(),a())}),r("toEdge fromEdge lock unlock",()=>{a()}),r("destroy",()=>{d()}),r("enable disable",()=>{let{nextEl:f,prevEl:y}=t.navigation;if(f=U(f),y=U(y),t.enabled){a();return}[...f,...y].filter(v=>!!v).forEach(v=>v.classList.add(t.params.navigation.lockClass))}),r("click",(f,y)=>{let{nextEl:v,prevEl:w}=t.navigation;v=U(v),w=U(w);const h=y.target;let m=w.includes(h)||v.includes(h);if(t.isElement&&!m){const x=y.path||y.composedPath&&y.composedPath();x&&(m=x.find(_=>v.includes(_)||w.includes(_)))}if(t.params.navigation.hideOnClick&&!m){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===h||t.pagination.el.contains(h)))return;let x;v.length?x=v[0].classList.contains(t.params.navigation.hiddenClass):w.length&&(x=w[0].classList.contains(t.params.navigation.hiddenClass)),i(x===!0?"navigationShow":"navigationHide"),[...v,...w].filter(_=>!!_).forEach(_=>_.classList.toggle(t.params.navigation.hiddenClass))}});const p=()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),u(),a()},g=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),d()};Object.assign(t.navigation,{enable:p,disable:g,update:a,init:u,destroy:d})}function Ye(e){return e===void 0&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function rd(e){let{swiper:t,extendParams:n,on:r,emit:i}=e;const s="swiper-pagination";n({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:h=>h,formatFractionTotal:h=>h,bulletClass:`${s}-bullet`,bulletActiveClass:`${s}-bullet-active`,modifierClass:`${s}-`,currentClass:`${s}-current`,totalClass:`${s}-total`,hiddenClass:`${s}-hidden`,progressbarFillClass:`${s}-progressbar-fill`,progressbarOppositeClass:`${s}-progressbar-opposite`,clickableClass:`${s}-clickable`,lockClass:`${s}-lock`,horizontalClass:`${s}-horizontal`,verticalClass:`${s}-vertical`,paginationDisabledClass:`${s}-disabled`}}),t.pagination={el:null,bullets:[]};let o,a=0;function c(){return!t.params.pagination.el||!t.pagination.el||Array.isArray(t.pagination.el)&&t.pagination.el.length===0}function l(h,m){const{bulletActiveClass:x}=t.params.pagination;h&&(h=h[`${m==="prev"?"previous":"next"}ElementSibling`],h&&(h.classList.add(`${x}-${m}`),h=h[`${m==="prev"?"previous":"next"}ElementSibling`],h&&h.classList.add(`${x}-${m}-${m}`)))}function u(h,m,x){if(h=h%x,m=m%x,m===h+1)return"next";if(m===h-1)return"previous"}function d(h){const m=h.target.closest(Ye(t.params.pagination.bulletClass));if(!m)return;h.preventDefault();const x=Mt(m)*t.params.slidesPerGroup;if(t.params.loop){if(t.realIndex===x)return;const _=u(t.realIndex,x,t.slides.length);_==="next"?t.slideNext():_==="previous"?t.slidePrev():t.slideToLoop(x)}else t.slideTo(x)}function p(){const h=t.rtl,m=t.params.pagination;if(c())return;let x=t.pagination.el;x=U(x);let _,T;const M=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.slides.length,O=t.params.loop?Math.ceil(M/t.params.slidesPerGroup):t.snapGrid.length;if(t.params.loop?(T=t.previousRealIndex||0,_=t.params.slidesPerGroup>1?Math.floor(t.realIndex/t.params.slidesPerGroup):t.realIndex):typeof t.snapIndex<"u"?(_=t.snapIndex,T=t.previousSnapIndex):(T=t.previousIndex||0,_=t.activeIndex||0),m.type==="bullets"&&t.pagination.bullets&&t.pagination.bullets.length>0){const L=t.pagination.bullets;let C,S,E;if(m.dynamicBullets&&(o=In(L[0],t.isHorizontal()?"width":"height",!0),x.forEach(P=>{P.style[t.isHorizontal()?"width":"height"]=`${o*(m.dynamicMainBullets+4)}px`}),m.dynamicMainBullets>1&&T!==void 0&&(a+=_-(T||0),a>m.dynamicMainBullets-1?a=m.dynamicMainBullets-1:a<0&&(a=0)),C=Math.max(_-a,0),S=C+(Math.min(L.length,m.dynamicMainBullets)-1),E=(S+C)/2),L.forEach(P=>{const A=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(R=>`${m.bulletActiveClass}${R}`)].map(R=>typeof R=="string"&&R.includes(" ")?R.split(" "):R).flat();P.classList.remove(...A)}),x.length>1)L.forEach(P=>{const A=Mt(P);A===_?P.classList.add(...m.bulletActiveClass.split(" ")):t.isElement&&P.setAttribute("part","bullet"),m.dynamicBullets&&(A>=C&&A<=S&&P.classList.add(...`${m.bulletActiveClass}-main`.split(" ")),A===C&&l(P,"prev"),A===S&&l(P,"next"))});else{const P=L[_];if(P&&P.classList.add(...m.bulletActiveClass.split(" ")),t.isElement&&L.forEach((A,R)=>{A.setAttribute("part",R===_?"bullet-active":"bullet")}),m.dynamicBullets){const A=L[C],R=L[S];for(let I=C;I<=S;I+=1)L[I]&&L[I].classList.add(...`${m.bulletActiveClass}-main`.split(" "));l(A,"prev"),l(R,"next")}}if(m.dynamicBullets){const P=Math.min(L.length,m.dynamicMainBullets+4),A=(o*P-o)/2-E*o,R=h?"right":"left";L.forEach(I=>{I.style[t.isHorizontal()?R:"top"]=`${A}px`})}}x.forEach((L,C)=>{if(m.type==="fraction"&&(L.querySelectorAll(Ye(m.currentClass)).forEach(S=>{S.textContent=m.formatFractionCurrent(_+1)}),L.querySelectorAll(Ye(m.totalClass)).forEach(S=>{S.textContent=m.formatFractionTotal(O)})),m.type==="progressbar"){let S;m.progressbarOpposite?S=t.isHorizontal()?"vertical":"horizontal":S=t.isHorizontal()?"horizontal":"vertical";const E=(_+1)/O;let P=1,A=1;S==="horizontal"?P=E:A=E,L.querySelectorAll(Ye(m.progressbarFillClass)).forEach(R=>{R.style.transform=`translate3d(0,0,0) scaleX(${P}) scaleY(${A})`,R.style.transitionDuration=`${t.params.speed}ms`})}m.type==="custom"&&m.renderCustom?(zr(L,m.renderCustom(t,_+1,O)),C===0&&i("paginationRender",L)):(C===0&&i("paginationRender",L),i("paginationUpdate",L)),t.params.watchOverflow&&t.enabled&&L.classList[t.isLocked?"add":"remove"](m.lockClass)})}function g(){const h=t.params.pagination;if(c())return;const m=t.virtual&&t.params.virtual.enabled?t.virtual.slides.length:t.grid&&t.params.grid.rows>1?t.slides.length/Math.ceil(t.params.grid.rows):t.slides.length;let x=t.pagination.el;x=U(x);let _="";if(h.type==="bullets"){let T=t.params.loop?Math.ceil(m/t.params.slidesPerGroup):t.snapGrid.length;t.params.freeMode&&t.params.freeMode.enabled&&T>m&&(T=m);for(let M=0;M<T;M+=1)h.renderBullet?_+=h.renderBullet.call(t,M,h.bulletClass):_+=`<${h.bulletElement} ${t.isElement?'part="bullet"':""} class="${h.bulletClass}"></${h.bulletElement}>`}h.type==="fraction"&&(h.renderFraction?_=h.renderFraction.call(t,h.currentClass,h.totalClass):_=`<span class="${h.currentClass}"></span> / <span class="${h.totalClass}"></span>`),h.type==="progressbar"&&(h.renderProgressbar?_=h.renderProgressbar.call(t,h.progressbarFillClass):_=`<span class="${h.progressbarFillClass}"></span>`),t.pagination.bullets=[],x.forEach(T=>{h.type!=="custom"&&zr(T,_||""),h.type==="bullets"&&t.pagination.bullets.push(...T.querySelectorAll(Ye(h.bulletClass)))}),h.type!=="custom"&&i("paginationRender",x[0])}function f(){t.params.pagination=Ds(t,t.originalParams.pagination,t.params.pagination,{el:"swiper-pagination"});const h=t.params.pagination;if(!h.el)return;let m;typeof h.el=="string"&&t.isElement&&(m=t.el.querySelector(h.el)),!m&&typeof h.el=="string"&&(m=[...document.querySelectorAll(h.el)]),m||(m=h.el),!(!m||m.length===0)&&(t.params.uniqueNavElements&&typeof h.el=="string"&&Array.isArray(m)&&m.length>1&&(m=[...t.el.querySelectorAll(h.el)],m.length>1&&(m=m.find(x=>Ps(x,".swiper")[0]===t.el))),Array.isArray(m)&&m.length===1&&(m=m[0]),Object.assign(t.pagination,{el:m}),m=U(m),m.forEach(x=>{h.type==="bullets"&&h.clickable&&x.classList.add(...(h.clickableClass||"").split(" ")),x.classList.add(h.modifierClass+h.type),x.classList.add(t.isHorizontal()?h.horizontalClass:h.verticalClass),h.type==="bullets"&&h.dynamicBullets&&(x.classList.add(`${h.modifierClass}${h.type}-dynamic`),a=0,h.dynamicMainBullets<1&&(h.dynamicMainBullets=1)),h.type==="progressbar"&&h.progressbarOpposite&&x.classList.add(h.progressbarOppositeClass),h.clickable&&x.addEventListener("click",d),t.enabled||x.classList.add(h.lockClass)}))}function y(){const h=t.params.pagination;if(c())return;let m=t.pagination.el;m&&(m=U(m),m.forEach(x=>{x.classList.remove(h.hiddenClass),x.classList.remove(h.modifierClass+h.type),x.classList.remove(t.isHorizontal()?h.horizontalClass:h.verticalClass),h.clickable&&(x.classList.remove(...(h.clickableClass||"").split(" ")),x.removeEventListener("click",d))})),t.pagination.bullets&&t.pagination.bullets.forEach(x=>x.classList.remove(...h.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!t.pagination||!t.pagination.el)return;const h=t.params.pagination;let{el:m}=t.pagination;m=U(m),m.forEach(x=>{x.classList.remove(h.horizontalClass,h.verticalClass),x.classList.add(t.isHorizontal()?h.horizontalClass:h.verticalClass)})}),r("init",()=>{t.params.pagination.enabled===!1?w():(f(),g(),p())}),r("activeIndexChange",()=>{typeof t.snapIndex>"u"&&p()}),r("snapIndexChange",()=>{p()}),r("snapGridLengthChange",()=>{g(),p()}),r("destroy",()=>{y()}),r("enable disable",()=>{let{el:h}=t.pagination;h&&(h=U(h),h.forEach(m=>m.classList[t.enabled?"remove":"add"](t.params.pagination.lockClass)))}),r("lock unlock",()=>{p()}),r("click",(h,m)=>{const x=m.target,_=U(t.pagination.el);if(t.params.pagination.el&&t.params.pagination.hideOnClick&&_&&_.length>0&&!x.classList.contains(t.params.pagination.bulletClass)){if(t.navigation&&(t.navigation.nextEl&&x===t.navigation.nextEl||t.navigation.prevEl&&x===t.navigation.prevEl))return;const T=_[0].classList.contains(t.params.pagination.hiddenClass);i(T===!0?"paginationShow":"paginationHide"),_.forEach(M=>M.classList.toggle(t.params.pagination.hiddenClass))}});const v=()=>{t.el.classList.remove(t.params.pagination.paginationDisabledClass);let{el:h}=t.pagination;h&&(h=U(h),h.forEach(m=>m.classList.remove(t.params.pagination.paginationDisabledClass))),f(),g(),p()},w=()=>{t.el.classList.add(t.params.pagination.paginationDisabledClass);let{el:h}=t.pagination;h&&(h=U(h),h.forEach(m=>m.classList.add(t.params.pagination.paginationDisabledClass))),y()};Object.assign(t.pagination,{enable:v,disable:w,render:g,update:p,init:f,destroy:y})}function id(e){let{swiper:t,extendParams:n,on:r,emit:i,params:s}=e;t.autoplay={running:!1,paused:!1,timeLeft:0},n({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let o,a,c=s&&s.autoplay?s.autoplay.delay:3e3,l=s&&s.autoplay?s.autoplay.delay:3e3,u,d=new Date().getTime(),p,g,f,y,v,w,h;function m(k){!t||t.destroyed||!t.wrapperEl||k.target===t.wrapperEl&&(t.wrapperEl.removeEventListener("transitionend",m),!(h||k.detail&&k.detail.bySwiperTouchMove)&&C())}const x=()=>{if(t.destroyed||!t.autoplay.running)return;t.autoplay.paused?p=!0:p&&(l=u,p=!1);const k=t.autoplay.paused?u:d+l-new Date().getTime();t.autoplay.timeLeft=k,i("autoplayTimeLeft",k,k/c),a=requestAnimationFrame(()=>{x()})},_=()=>{let k;return t.virtual&&t.params.virtual.enabled?k=t.slides.find(H=>H.classList.contains("swiper-slide-active")):k=t.slides[t.activeIndex],k?parseInt(k.getAttribute("data-swiper-autoplay"),10):void 0},T=k=>{if(t.destroyed||!t.autoplay.running)return;cancelAnimationFrame(a),x();let z=typeof k>"u"?t.params.autoplay.delay:k;c=t.params.autoplay.delay,l=t.params.autoplay.delay;const H=_();!Number.isNaN(H)&&H>0&&typeof k>"u"&&(z=H,c=H,l=H),u=z;const ie=t.params.speed,ce=()=>{!t||t.destroyed||(t.params.autoplay.reverseDirection?!t.isBeginning||t.params.loop||t.params.rewind?(t.slidePrev(ie,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(t.slides.length-1,ie,!0,!0),i("autoplay")):!t.isEnd||t.params.loop||t.params.rewind?(t.slideNext(ie,!0,!0),i("autoplay")):t.params.autoplay.stopOnLastSlide||(t.slideTo(0,ie,!0,!0),i("autoplay")),t.params.cssMode&&(d=new Date().getTime(),requestAnimationFrame(()=>{T()})))};return z>0?(clearTimeout(o),o=setTimeout(()=>{ce()},z)):requestAnimationFrame(()=>{ce()}),z},M=()=>{d=new Date().getTime(),t.autoplay.running=!0,T(),i("autoplayStart")},O=()=>{t.autoplay.running=!1,clearTimeout(o),cancelAnimationFrame(a),i("autoplayStop")},L=(k,z)=>{if(t.destroyed||!t.autoplay.running)return;clearTimeout(o),k||(w=!0);const H=()=>{i("autoplayPause"),t.params.autoplay.waitForTransition?t.wrapperEl.addEventListener("transitionend",m):C()};if(t.autoplay.paused=!0,z){v&&(u=t.params.autoplay.delay),v=!1,H();return}u=(u||t.params.autoplay.delay)-(new Date().getTime()-d),!(t.isEnd&&u<0&&!t.params.loop)&&(u<0&&(u=0),H())},C=()=>{t.isEnd&&u<0&&!t.params.loop||t.destroyed||!t.autoplay.running||(d=new Date().getTime(),w?(w=!1,T(u)):T(),t.autoplay.paused=!1,i("autoplayResume"))},S=()=>{if(t.destroyed||!t.autoplay.running)return;const k=ge();k.visibilityState==="hidden"&&(w=!0,L(!0)),k.visibilityState==="visible"&&C()},E=k=>{k.pointerType==="mouse"&&(w=!0,h=!0,!(t.animating||t.autoplay.paused)&&L(!0))},P=k=>{k.pointerType==="mouse"&&(h=!1,t.autoplay.paused&&C())},A=()=>{t.params.autoplay.pauseOnMouseEnter&&(t.el.addEventListener("pointerenter",E),t.el.addEventListener("pointerleave",P))},R=()=>{t.el&&typeof t.el!="string"&&(t.el.removeEventListener("pointerenter",E),t.el.removeEventListener("pointerleave",P))},I=()=>{ge().addEventListener("visibilitychange",S)},B=()=>{ge().removeEventListener("visibilitychange",S)};r("init",()=>{t.params.autoplay.enabled&&(A(),I(),M())}),r("destroy",()=>{R(),B(),t.autoplay.running&&O()}),r("_freeModeStaticRelease",()=>{(f||w)&&C()}),r("_freeModeNoMomentumRelease",()=>{t.params.autoplay.disableOnInteraction?O():L(!0,!0)}),r("beforeTransitionStart",(k,z,H)=>{t.destroyed||!t.autoplay.running||(H||!t.params.autoplay.disableOnInteraction?L(!0,!0):O())}),r("sliderFirstMove",()=>{if(!(t.destroyed||!t.autoplay.running)){if(t.params.autoplay.disableOnInteraction){O();return}g=!0,f=!1,w=!1,y=setTimeout(()=>{w=!0,f=!0,L(!0)},200)}}),r("touchEnd",()=>{if(!(t.destroyed||!t.autoplay.running||!g)){if(clearTimeout(y),clearTimeout(o),t.params.autoplay.disableOnInteraction){f=!1,g=!1;return}f&&t.params.cssMode&&C(),f=!1,g=!1}}),r("slideChange",()=>{t.destroyed||!t.autoplay.running||(v=!0)}),Object.assign(t.autoplay,{start:M,stop:O,pause:L,resume:C})}function sd(e){const{effect:t,swiper:n,on:r,setTranslate:i,setTransition:s,overwriteParams:o,perspective:a,recreateShadows:c,getEffectParams:l}=e;r("beforeInit",()=>{if(n.params.effect!==t)return;n.classNames.push(`${n.params.containerModifierClass}${t}`),a&&a()&&n.classNames.push(`${n.params.containerModifierClass}3d`);const d=o?o():{};Object.assign(n.params,d),Object.assign(n.originalParams,d)}),r("setTranslate _virtualUpdated",()=>{n.params.effect===t&&i()}),r("setTransition",(d,p)=>{n.params.effect===t&&s(p)}),r("transitionEnd",()=>{if(n.params.effect===t&&c){if(!l||!l().slideShadows)return;n.slides.forEach(d=>{d.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(p=>p.remove())}),c()}});let u;r("virtualUpdate",()=>{n.params.effect===t&&(n.slides.length||(u=!0),requestAnimationFrame(()=>{u&&n.slides&&n.slides.length&&(i(),u=!1)}))})}function od(e,t){const n=As(t);return n!==t&&(n.style.backfaceVisibility="hidden",n.style["-webkit-backface-visibility"]="hidden"),n}function ad(e){let{swiper:t,duration:n,transformElements:r,allSlides:i}=e;const{activeIndex:s}=t,o=a=>a.parentElement?a.parentElement:t.slides.find(l=>l.shadowRoot&&l.shadowRoot===a.parentNode);if(t.params.virtualTranslate&&n!==0){let a=!1,c;i?c=r:c=r.filter(l=>{const u=l.classList.contains("swiper-slide-transform")?o(l):l;return t.getSlideIndex(u)===s}),c.forEach(l=>{Uc(l,()=>{if(a||!t||t.destroyed)return;a=!0,t.animating=!1;const u=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});t.wrapperEl.dispatchEvent(u)})})}}function ld(e){let{swiper:t,extendParams:n,on:r}=e;n({fadeEffect:{crossFade:!1}}),sd({effect:"fade",swiper:t,on:r,setTranslate:()=>{const{slides:o}=t,a=t.params.fadeEffect;for(let c=0;c<o.length;c+=1){const l=t.slides[c];let d=-l.swiperSlideOffset;t.params.virtualTranslate||(d-=t.translate);let p=0;t.isHorizontal()||(p=d,d=0);const g=t.params.fadeEffect.crossFade?Math.max(1-Math.abs(l.progress),0):1+Math.min(Math.max(l.progress,-1),0),f=od(a,l);f.style.opacity=g,f.style.transform=`translate3d(${d}px, ${p}px, 0px)`}},setTransition:o=>{const a=t.slides.map(c=>As(c));a.forEach(c=>{c.style.transitionDuration=`${o}ms`}),ad({swiper:t,duration:o,transformElements:a,allSlides:!0})},overwriteParams:()=>({slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!t.params.cssMode})})}var cd=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ud(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ns={exports:{}};(function(e,t){(function(n,r){e.exports=r()})(cd,function(){return function(n){function r(s){if(i[s])return i[s].exports;var o=i[s]={exports:{},id:s,loaded:!1};return n[s].call(o.exports,o,o.exports,r),o.loaded=!0,o.exports}var i={};return r.m=n,r.c=i,r.p="dist/",r(0)}([function(n,r,i){function s(A){return A&&A.__esModule?A:{default:A}}var o=Object.assign||function(A){for(var R=1;R<arguments.length;R++){var I=arguments[R];for(var B in I)Object.prototype.hasOwnProperty.call(I,B)&&(A[B]=I[B])}return A},a=i(1),c=(s(a),i(6)),l=s(c),u=i(7),d=s(u),p=i(8),g=s(p),f=i(9),y=s(f),v=i(10),w=s(v),h=i(11),m=s(h),x=i(14),_=s(x),T=[],M=!1,O={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},L=function(){var A=arguments.length>0&&arguments[0]!==void 0&&arguments[0];if(A&&(M=!0),M)return T=(0,m.default)(T,O),(0,w.default)(T,O.once),T},C=function(){T=(0,_.default)(),L()},S=function(){T.forEach(function(A,R){A.node.removeAttribute("data-aos"),A.node.removeAttribute("data-aos-easing"),A.node.removeAttribute("data-aos-duration"),A.node.removeAttribute("data-aos-delay")})},E=function(A){return A===!0||A==="mobile"&&y.default.mobile()||A==="phone"&&y.default.phone()||A==="tablet"&&y.default.tablet()||typeof A=="function"&&A()===!0},P=function(A){O=o(O,A),T=(0,_.default)();var R=document.all&&!window.atob;return E(O.disable)||R?S():(O.disableMutationObserver||g.default.isSupported()||(console.info(`
      aos: MutationObserver is not supported on this browser,
      code mutations observing has been disabled.
      You may have to call "refreshHard()" by yourself.
    `),O.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",O.easing),document.querySelector("body").setAttribute("data-aos-duration",O.duration),document.querySelector("body").setAttribute("data-aos-delay",O.delay),O.startEvent==="DOMContentLoaded"&&["complete","interactive"].indexOf(document.readyState)>-1?L(!0):O.startEvent==="load"?window.addEventListener(O.startEvent,function(){L(!0)}):document.addEventListener(O.startEvent,function(){L(!0)}),window.addEventListener("resize",(0,d.default)(L,O.debounceDelay,!0)),window.addEventListener("orientationchange",(0,d.default)(L,O.debounceDelay,!0)),window.addEventListener("scroll",(0,l.default)(function(){(0,w.default)(T,O.once)},O.throttleDelay)),O.disableMutationObserver||g.default.ready("[data-aos]",C),T)};n.exports={init:P,refresh:L,refreshHard:C}},function(n,r){},,,,,function(n,r){(function(i){function s(E,P,A){function R(N){var J=ne,xe=de;return ne=de=void 0,we=N,W=E.apply(xe,J)}function I(N){return we=N,G=setTimeout(z,P),be?R(N):W}function B(N){var J=N-ee,xe=N-we,lr=P-J;return ve?C(lr,fe-xe):lr}function k(N){var J=N-ee,xe=N-we;return ee===void 0||J>=P||J<0||ve&&xe>=fe}function z(){var N=S();return k(N)?H(N):void(G=setTimeout(z,B(N)))}function H(N){return G=void 0,$&&ne?R(N):(ne=de=void 0,W)}function ie(){G!==void 0&&clearTimeout(G),we=0,ne=ee=de=G=void 0}function ce(){return G===void 0?W:H(S())}function ue(){var N=S(),J=k(N);if(ne=arguments,de=this,ee=N,J){if(G===void 0)return I(ee);if(ve)return G=setTimeout(z,P),R(ee)}return G===void 0&&(G=setTimeout(z,P)),W}var ne,de,fe,W,G,ee,we=0,be=!1,ve=!1,$=!0;if(typeof E!="function")throw new TypeError(p);return P=u(P)||0,a(A)&&(be=!!A.leading,ve="maxWait"in A,fe=ve?L(u(A.maxWait)||0,P):fe,$="trailing"in A?!!A.trailing:$),ue.cancel=ie,ue.flush=ce,ue}function o(E,P,A){var R=!0,I=!0;if(typeof E!="function")throw new TypeError(p);return a(A)&&(R="leading"in A?!!A.leading:R,I="trailing"in A?!!A.trailing:I),s(E,P,{leading:R,maxWait:P,trailing:I})}function a(E){var P=typeof E>"u"?"undefined":d(E);return!!E&&(P=="object"||P=="function")}function c(E){return!!E&&(typeof E>"u"?"undefined":d(E))=="object"}function l(E){return(typeof E>"u"?"undefined":d(E))=="symbol"||c(E)&&O.call(E)==f}function u(E){if(typeof E=="number")return E;if(l(E))return g;if(a(E)){var P=typeof E.valueOf=="function"?E.valueOf():E;E=a(P)?P+"":P}if(typeof E!="string")return E===0?E:+E;E=E.replace(y,"");var A=w.test(E);return A||h.test(E)?m(E.slice(2),A?2:8):v.test(E)?g:+E}var d=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(E){return typeof E}:function(E){return E&&typeof Symbol=="function"&&E.constructor===Symbol&&E!==Symbol.prototype?"symbol":typeof E},p="Expected a function",g=NaN,f="[object Symbol]",y=/^\s+|\s+$/g,v=/^[-+]0x[0-9a-f]+$/i,w=/^0b[01]+$/i,h=/^0o[0-7]+$/i,m=parseInt,x=(typeof i>"u"?"undefined":d(i))=="object"&&i&&i.Object===Object&&i,_=(typeof self>"u"?"undefined":d(self))=="object"&&self&&self.Object===Object&&self,T=x||_||Function("return this")(),M=Object.prototype,O=M.toString,L=Math.max,C=Math.min,S=function(){return T.Date.now()};n.exports=o}).call(r,function(){return this}())},function(n,r){(function(i){function s(S,E,P){function A($){var N=ue,J=ne;return ue=ne=void 0,ee=$,fe=S.apply(J,N)}function R($){return ee=$,W=setTimeout(k,E),we?A($):fe}function I($){var N=$-G,J=$-ee,xe=E-N;return be?L(xe,de-J):xe}function B($){var N=$-G,J=$-ee;return G===void 0||N>=E||N<0||be&&J>=de}function k(){var $=C();return B($)?z($):void(W=setTimeout(k,I($)))}function z($){return W=void 0,ve&&ue?A($):(ue=ne=void 0,fe)}function H(){W!==void 0&&clearTimeout(W),ee=0,ue=G=ne=W=void 0}function ie(){return W===void 0?fe:z(C())}function ce(){var $=C(),N=B($);if(ue=arguments,ne=this,G=$,N){if(W===void 0)return R(G);if(be)return W=setTimeout(k,E),A(G)}return W===void 0&&(W=setTimeout(k,E)),fe}var ue,ne,de,fe,W,G,ee=0,we=!1,be=!1,ve=!0;if(typeof S!="function")throw new TypeError(d);return E=l(E)||0,o(P)&&(we=!!P.leading,be="maxWait"in P,de=be?O(l(P.maxWait)||0,E):de,ve="trailing"in P?!!P.trailing:ve),ce.cancel=H,ce.flush=ie,ce}function o(S){var E=typeof S>"u"?"undefined":u(S);return!!S&&(E=="object"||E=="function")}function a(S){return!!S&&(typeof S>"u"?"undefined":u(S))=="object"}function c(S){return(typeof S>"u"?"undefined":u(S))=="symbol"||a(S)&&M.call(S)==g}function l(S){if(typeof S=="number")return S;if(c(S))return p;if(o(S)){var E=typeof S.valueOf=="function"?S.valueOf():S;S=o(E)?E+"":E}if(typeof S!="string")return S===0?S:+S;S=S.replace(f,"");var P=v.test(S);return P||w.test(S)?h(S.slice(2),P?2:8):y.test(S)?p:+S}var u=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(S){return typeof S}:function(S){return S&&typeof Symbol=="function"&&S.constructor===Symbol&&S!==Symbol.prototype?"symbol":typeof S},d="Expected a function",p=NaN,g="[object Symbol]",f=/^\s+|\s+$/g,y=/^[-+]0x[0-9a-f]+$/i,v=/^0b[01]+$/i,w=/^0o[0-7]+$/i,h=parseInt,m=(typeof i>"u"?"undefined":u(i))=="object"&&i&&i.Object===Object&&i,x=(typeof self>"u"?"undefined":u(self))=="object"&&self&&self.Object===Object&&self,_=m||x||Function("return this")(),T=Object.prototype,M=T.toString,O=Math.max,L=Math.min,C=function(){return _.Date.now()};n.exports=s}).call(r,function(){return this}())},function(n,r){function i(u){var d=void 0,p=void 0;for(d=0;d<u.length;d+=1)if(p=u[d],p.dataset&&p.dataset.aos||p.children&&i(p.children))return!0;return!1}function s(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function o(){return!!s()}function a(u,d){var p=window.document,g=s(),f=new g(c);l=d,f.observe(p.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}function c(u){u&&u.forEach(function(d){var p=Array.prototype.slice.call(d.addedNodes),g=Array.prototype.slice.call(d.removedNodes),f=p.concat(g);if(i(f))return l()})}Object.defineProperty(r,"__esModule",{value:!0});var l=function(){};r.default={isSupported:o,ready:a}},function(n,r){function i(p,g){if(!(p instanceof g))throw new TypeError("Cannot call a class as a function")}function s(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(r,"__esModule",{value:!0});var o=function(){function p(g,f){for(var y=0;y<f.length;y++){var v=f[y];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(g,v.key,v)}}return function(g,f,y){return f&&p(g.prototype,f),y&&p(g,y),g}}(),a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,c=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,l=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,u=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,d=function(){function p(){i(this,p)}return o(p,[{key:"phone",value:function(){var g=s();return!(!a.test(g)&&!c.test(g.substr(0,4)))}},{key:"mobile",value:function(){var g=s();return!(!l.test(g)&&!u.test(g.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),p}();r.default=new d},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var i=function(o,a,c){var l=o.node.getAttribute("data-aos-once");a>o.position?o.node.classList.add("aos-animate"):typeof l<"u"&&(l==="false"||!c&&l!=="true")&&o.node.classList.remove("aos-animate")},s=function(o,a){var c=window.pageYOffset,l=window.innerHeight;o.forEach(function(u,d){i(u,l+c,a)})};r.default=s},function(n,r,i){function s(l){return l&&l.__esModule?l:{default:l}}Object.defineProperty(r,"__esModule",{value:!0});var o=i(12),a=s(o),c=function(l,u){return l.forEach(function(d,p){d.node.classList.add("aos-init"),d.position=(0,a.default)(d.node,u.offset)}),l};r.default=c},function(n,r,i){function s(l){return l&&l.__esModule?l:{default:l}}Object.defineProperty(r,"__esModule",{value:!0});var o=i(13),a=s(o),c=function(l,u){var d=0,p=0,g=window.innerHeight,f={offset:l.getAttribute("data-aos-offset"),anchor:l.getAttribute("data-aos-anchor"),anchorPlacement:l.getAttribute("data-aos-anchor-placement")};switch(f.offset&&!isNaN(f.offset)&&(p=parseInt(f.offset)),f.anchor&&document.querySelectorAll(f.anchor)&&(l=document.querySelectorAll(f.anchor)[0]),d=(0,a.default)(l).top,f.anchorPlacement){case"top-bottom":break;case"center-bottom":d+=l.offsetHeight/2;break;case"bottom-bottom":d+=l.offsetHeight;break;case"top-center":d+=g/2;break;case"bottom-center":d+=g/2+l.offsetHeight;break;case"center-center":d+=g/2+l.offsetHeight/2;break;case"top-top":d+=g;break;case"bottom-top":d+=l.offsetHeight+g;break;case"center-top":d+=l.offsetHeight/2+g}return f.anchorPlacement||f.offset||isNaN(u)||(p=u),d+p};r.default=c},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var i=function(s){for(var o=0,a=0;s&&!isNaN(s.offsetLeft)&&!isNaN(s.offsetTop);)o+=s.offsetLeft-(s.tagName!="BODY"?s.scrollLeft:0),a+=s.offsetTop-(s.tagName!="BODY"?s.scrollTop:0),s=s.offsetParent;return{top:a,left:o}};r.default=i},function(n,r){Object.defineProperty(r,"__esModule",{value:!0});var i=function(s){return s=s||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(s,function(o){return{node:o}})};r.default=i}])})})(Ns);var dd=Ns.exports;const fd=ud(dd);Ut.plugin(kc);Ut.plugin(Dc);window.Alpine=Ut;Ut.start();Z.use([nd,rd,id,ld]);document.addEventListener("DOMContentLoaded",function(){fd.init({duration:800,easing:"ease-in-out",once:!0,offset:100})});document.addEventListener("DOMContentLoaded",function(){const e=document.getElementById("mobile-menu-button"),t=document.getElementById("mobile-menu");e&&t&&e.addEventListener("click",function(){t.classList.toggle("hidden")}),document.querySelectorAll('a[href^="#"]').forEach(o=>{o.addEventListener("click",function(a){a.preventDefault();const c=document.querySelector(this.getAttribute("href"));c&&c.scrollIntoView({behavior:"smooth",block:"start"})})});function n(){document.querySelectorAll(".counter").forEach(a=>{const c=parseInt(a.getAttribute("data-target")),u=c/(2e3/16);let d=0;const p=()=>{d+=u,d<c?(a.textContent=Math.floor(d),requestAnimationFrame(p)):a.textContent=c};p()})}const r={threshold:.5,rootMargin:"0px 0px -100px 0px"},i=new IntersectionObserver(o=>{o.forEach(a=>{a.isIntersecting&&(n(),i.unobserve(a.target))})},r),s=document.querySelector(".stats-section");s&&i.observe(s),pd()});function pd(){new Z(".partners-swiper",{slidesPerView:2,spaceBetween:30,autoplay:{delay:3e3,disableOnInteraction:!1},loop:!0,breakpoints:{640:{slidesPerView:3},768:{slidesPerView:4},1024:{slidesPerView:6}}}),new Z(".testimonials-swiper",{slidesPerView:1,spaceBetween:30,autoplay:{delay:5e3,disableOnInteraction:!1},pagination:{el:".swiper-pagination",clickable:!0},loop:!0,breakpoints:{768:{slidesPerView:2},1024:{slidesPerView:3}}})}
