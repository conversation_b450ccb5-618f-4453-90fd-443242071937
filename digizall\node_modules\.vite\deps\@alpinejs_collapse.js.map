{"version": 3, "sources": ["../../@alpinejs/collapse/dist/module.esm.js"], "sourcesContent": ["// packages/collapse/src/index.js\nfunction src_default(Alpine) {\n  Alpine.directive(\"collapse\", collapse);\n  collapse.inline = (el, { modifiers }) => {\n    if (!modifiers.includes(\"min\"))\n      return;\n    el._x_doShow = () => {\n    };\n    el._x_doHide = () => {\n    };\n  };\n  function collapse(el, { modifiers }) {\n    let duration = modifierValue(modifiers, \"duration\", 250) / 1e3;\n    let floor = modifierValue(modifiers, \"min\", 0);\n    let fullyHide = !modifiers.includes(\"min\");\n    if (!el._x_isShown)\n      el.style.height = `${floor}px`;\n    if (!el._x_isShown && fullyHide)\n      el.hidden = true;\n    if (!el._x_isShown)\n      el.style.overflow = \"hidden\";\n    let setFunction = (el2, styles) => {\n      let revertFunction = Alpine.setStyles(el2, styles);\n      return styles.height ? () => {\n      } : revertFunction;\n    };\n    let transitionStyles = {\n      transitionProperty: \"height\",\n      transitionDuration: `${duration}s`,\n      transitionTimingFunction: \"cubic-bezier(0.4, 0.0, 0.2, 1)\"\n    };\n    el._x_transition = {\n      in(before = () => {\n      }, after = () => {\n      }) {\n        if (fullyHide)\n          el.hidden = false;\n        if (fullyHide)\n          el.style.display = null;\n        let current = el.getBoundingClientRect().height;\n        el.style.height = \"auto\";\n        let full = el.getBoundingClientRect().height;\n        if (current === full) {\n          current = floor;\n        }\n        Alpine.transition(el, Alpine.setStyles, {\n          during: transitionStyles,\n          start: { height: current + \"px\" },\n          end: { height: full + \"px\" }\n        }, () => el._x_isShown = true, () => {\n          if (Math.abs(el.getBoundingClientRect().height - full) < 1) {\n            el.style.overflow = null;\n          }\n        });\n      },\n      out(before = () => {\n      }, after = () => {\n      }) {\n        let full = el.getBoundingClientRect().height;\n        Alpine.transition(el, setFunction, {\n          during: transitionStyles,\n          start: { height: full + \"px\" },\n          end: { height: floor + \"px\" }\n        }, () => el.style.overflow = \"hidden\", () => {\n          el._x_isShown = false;\n          if (el.style.height == `${floor}px` && fullyHide) {\n            el.style.display = \"none\";\n            el.hidden = true;\n          }\n        });\n      }\n    };\n  }\n}\nfunction modifierValue(modifiers, key, fallback) {\n  if (modifiers.indexOf(key) === -1)\n    return fallback;\n  const rawValue = modifiers[modifiers.indexOf(key) + 1];\n  if (!rawValue)\n    return fallback;\n  if (key === \"duration\") {\n    let match = rawValue.match(/([0-9]+)ms/);\n    if (match)\n      return match[1];\n  }\n  if (key === \"min\") {\n    let match = rawValue.match(/([0-9]+)px/);\n    if (match)\n      return match[1];\n  }\n  return rawValue;\n}\n\n// packages/collapse/builds/module.js\nvar module_default = src_default;\nexport {\n  src_default as collapse,\n  module_default as default\n};\n"], "mappings": ";;;AACA,SAAS,YAAY,QAAQ;AAC3B,SAAO,UAAU,YAAY,QAAQ;AACrC,WAAS,SAAS,CAAC,IAAI,EAAE,UAAU,MAAM;AACvC,QAAI,CAAC,UAAU,SAAS,KAAK;AAC3B;AACF,OAAG,YAAY,MAAM;AAAA,IACrB;AACA,OAAG,YAAY,MAAM;AAAA,IACrB;AAAA,EACF;AACA,WAAS,SAAS,IAAI,EAAE,UAAU,GAAG;AACnC,QAAI,WAAW,cAAc,WAAW,YAAY,GAAG,IAAI;AAC3D,QAAI,QAAQ,cAAc,WAAW,OAAO,CAAC;AAC7C,QAAI,YAAY,CAAC,UAAU,SAAS,KAAK;AACzC,QAAI,CAAC,GAAG;AACN,SAAG,MAAM,SAAS,GAAG,KAAK;AAC5B,QAAI,CAAC,GAAG,cAAc;AACpB,SAAG,SAAS;AACd,QAAI,CAAC,GAAG;AACN,SAAG,MAAM,WAAW;AACtB,QAAI,cAAc,CAAC,KAAK,WAAW;AACjC,UAAI,iBAAiB,OAAO,UAAU,KAAK,MAAM;AACjD,aAAO,OAAO,SAAS,MAAM;AAAA,MAC7B,IAAI;AAAA,IACN;AACA,QAAI,mBAAmB;AAAA,MACrB,oBAAoB;AAAA,MACpB,oBAAoB,GAAG,QAAQ;AAAA,MAC/B,0BAA0B;AAAA,IAC5B;AACA,OAAG,gBAAgB;AAAA,MACjB,GAAG,SAAS,MAAM;AAAA,MAClB,GAAG,QAAQ,MAAM;AAAA,MACjB,GAAG;AACD,YAAI;AACF,aAAG,SAAS;AACd,YAAI;AACF,aAAG,MAAM,UAAU;AACrB,YAAI,UAAU,GAAG,sBAAsB,EAAE;AACzC,WAAG,MAAM,SAAS;AAClB,YAAI,OAAO,GAAG,sBAAsB,EAAE;AACtC,YAAI,YAAY,MAAM;AACpB,oBAAU;AAAA,QACZ;AACA,eAAO,WAAW,IAAI,OAAO,WAAW;AAAA,UACtC,QAAQ;AAAA,UACR,OAAO,EAAE,QAAQ,UAAU,KAAK;AAAA,UAChC,KAAK,EAAE,QAAQ,OAAO,KAAK;AAAA,QAC7B,GAAG,MAAM,GAAG,aAAa,MAAM,MAAM;AACnC,cAAI,KAAK,IAAI,GAAG,sBAAsB,EAAE,SAAS,IAAI,IAAI,GAAG;AAC1D,eAAG,MAAM,WAAW;AAAA,UACtB;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,IAAI,SAAS,MAAM;AAAA,MACnB,GAAG,QAAQ,MAAM;AAAA,MACjB,GAAG;AACD,YAAI,OAAO,GAAG,sBAAsB,EAAE;AACtC,eAAO,WAAW,IAAI,aAAa;AAAA,UACjC,QAAQ;AAAA,UACR,OAAO,EAAE,QAAQ,OAAO,KAAK;AAAA,UAC7B,KAAK,EAAE,QAAQ,QAAQ,KAAK;AAAA,QAC9B,GAAG,MAAM,GAAG,MAAM,WAAW,UAAU,MAAM;AAC3C,aAAG,aAAa;AAChB,cAAI,GAAG,MAAM,UAAU,GAAG,KAAK,QAAQ,WAAW;AAChD,eAAG,MAAM,UAAU;AACnB,eAAG,SAAS;AAAA,UACd;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,cAAc,WAAW,KAAK,UAAU;AAC/C,MAAI,UAAU,QAAQ,GAAG,MAAM;AAC7B,WAAO;AACT,QAAM,WAAW,UAAU,UAAU,QAAQ,GAAG,IAAI,CAAC;AACrD,MAAI,CAAC;AACH,WAAO;AACT,MAAI,QAAQ,YAAY;AACtB,QAAI,QAAQ,SAAS,MAAM,YAAY;AACvC,QAAI;AACF,aAAO,MAAM,CAAC;AAAA,EAClB;AACA,MAAI,QAAQ,OAAO;AACjB,QAAI,QAAQ,SAAS,MAAM,YAAY;AACvC,QAAI;AACF,aAAO,MAAM,CAAC;AAAA,EAClB;AACA,SAAO;AACT;AAGA,IAAI,iBAAiB;", "names": []}