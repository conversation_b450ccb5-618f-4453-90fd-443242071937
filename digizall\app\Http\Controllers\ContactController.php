<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ContactSubmission;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    public function submit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:5000',
        ]);

        // Save to database
        ContactSubmission::create($validated);

        // Send notification email (optional)
        try {
            Mail::to('<EMAIL>')->send(new \App\Mail\ContactFormSubmission($validated));
        } catch (\Exception $e) {
            // Log error but don't fail the form submission
            \Log::error('Failed to send contact form email: ' . $e->getMessage());
        }

        return redirect()->route('contact')->with('success', 'Thank you for your message! We\'ll get back to you within 24 hours.');
    }
}
