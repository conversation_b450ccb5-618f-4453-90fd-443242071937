/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for MarketPro clone */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
  }
}

@layer components {
  .btn-primary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    font-weight: 600;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    transform: scale(1);
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: linear-gradient(to right, #1d4ed8, #7c3aed);
    transform: scale(1.05);
    text-decoration: none;
    color: white;
  }

  .btn-secondary {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 2px solid #2563eb;
    color: #2563eb;
    font-weight: 600;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: #2563eb;
    color: white;
    text-decoration: none;
  }

  .section-padding {
    padding: 4rem 0;
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding: 6rem 0;
    }
  }

  .container-custom {
    max-width: 80rem;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding: 0 2rem;
    }
  }

  .gradient-text {
    background: linear-gradient(to right, #2563eb, #9333ea);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .card-hover {
    transition: all 0.3s ease;
  }

  .card-hover:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: translateY(-0.5rem);
  }

  .nav-link {
    color: #374151;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .nav-link:hover {
    color: #2563eb;
    text-decoration: none;
  }

  .hero-text {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1.1;
  }

  @media (min-width: 1024px) {
    .hero-text {
      font-size: 4.5rem;
    }
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .bg-pattern {
    background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Gradient backgrounds */
  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
  }

  .from-blue-50 {
    --tw-gradient-from: #eff6ff;
    --tw-gradient-to: rgb(239 246 255 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-purple-50 {
    --tw-gradient-to: #faf5ff;
  }

  .from-blue-600 {
    --tw-gradient-from: #2563eb;
    --tw-gradient-to: rgb(37 99 235 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-purple-600 {
    --tw-gradient-to: #9333ea;
  }

  .from-green-50 {
    --tw-gradient-from: #f0fdf4;
    --tw-gradient-to: rgb(240 253 244 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-yellow-50 {
    --tw-gradient-from: #fefce8;
    --tw-gradient-to: rgb(254 252 232 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-red-50 {
    --tw-gradient-to: #fef2f2;
  }

  .from-green-600 {
    --tw-gradient-from: #16a34a;
    --tw-gradient-to: rgb(22 163 74 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .from-yellow-600 {
    --tw-gradient-from: #ca8a04;
    --tw-gradient-to: rgb(202 138 4 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }

  .to-red-600 {
    --tw-gradient-to: #dc2626;
  }

  .from-purple-600 {
    --tw-gradient-from: #9333ea;
    --tw-gradient-to: rgb(147 51 234 / 0);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

@keyframes scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

.animate-scroll {
  animation: scroll 20s linear infinite;
  width: 200%;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}